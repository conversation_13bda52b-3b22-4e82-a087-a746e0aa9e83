// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using ArtDesignFramework.Abstractions;
using Microsoft.Extensions.Logging;
using SkiaSharp;

namespace ArtDesignFramework.Core;

/// <summary>
/// Core rendering engine providing graphics capabilities using SkiaSharp
/// </summary>
[Testable]
public sealed class RenderEngine : IRenderEngine
{
    private readonly ILogger<RenderEngine> _logger;
    private readonly SemaphoreSlim _renderSemaphore = new(1, 1);
    private volatile bool _isInitialized;
    private volatile bool _disposed;
    private SKSurface? _surface;
    private SKCanvas? _canvas;

    /// <summary>
    /// Initializes a new instance of the <see cref="RenderEngine"/> class
    /// </summary>
    /// <param name="logger">Logger instance</param>
    public RenderEngine(ILogger<RenderEngine> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Gets whether the render engine is initialized
    /// </summary>
    public bool IsInitialized => _isInitialized;

    /// <summary>
    /// Initializes the graphics subsystem asynchronously
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the initialization operation</returns>
    public async Task InitializeAsync(CancellationToken cancellationToken = default)
    {
        if (_isInitialized)
            return;

        _logger.LogInformation("Initializing render engine...");

        try
        {
            await Task.Run(() =>
            {
                // Initialize SkiaSharp graphics backend
                var imageInfo = new SKImageInfo(1920, 1080, SKColorType.Rgba8888, SKAlphaType.Premul);
                _surface = SKSurface.Create(imageInfo);
                _canvas = _surface?.Canvas;

                if (_canvas == null)
                    throw new InvalidOperationException("Failed to create graphics canvas");

                _canvas.Clear(SKColors.Transparent);
                _isInitialized = true;
            }, cancellationToken);

            _logger.LogInformation("Render engine initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize render engine");
            throw;
        }
    }

    /// <summary>
    /// Renders the specified layers asynchronously
    /// </summary>
    /// <param name="layers">Layers to render</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the render operation</returns>
    public async Task RenderAsync(IEnumerable<ILayer> layers, CancellationToken cancellationToken = default)
    {
        ThrowIfNotInitialized();
        ArgumentNullException.ThrowIfNull(layers);

        await _renderSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (_canvas == null)
                throw new InvalidOperationException("Canvas is not available");

            _canvas.Clear(SKColors.Transparent);

            // Render layers in z-index order
            var sortedLayers = layers
                .Where(layer => layer.IsVisible)
                .OrderBy(layer => layer.ZIndex)
                .ToList();

            foreach (var layer in sortedLayers)
            {
                cancellationToken.ThrowIfCancellationRequested();
                await RenderLayerAsync(layer, cancellationToken);
            }

            _canvas.Flush();
            _logger.LogDebug("Rendered {LayerCount} layers", sortedLayers.Count);
        }
        finally
        {
            _renderSemaphore.Release();
        }
    }

    /// <summary>
    /// Renders a single layer
    /// </summary>
    /// <param name="layer">Layer to render</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the render operation</returns>
    private async Task RenderLayerAsync(ILayer layer, CancellationToken cancellationToken)
    {
        if (_canvas == null)
            return;

        // Apply layer opacity
        using var paint = new SKPaint
        {
            Color = SKColors.White.WithAlpha((byte)(layer.Opacity * 255)),
            IsAntialias = true
        };

        // Placeholder for actual layer rendering logic
        // This would be implemented based on layer type
        await Task.CompletedTask;
    }

    /// <summary>
    /// Shuts down the render engine and cleans up resources
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the shutdown operation</returns>
    public async Task ShutdownAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            return;

        _logger.LogInformation("Shutting down render engine...");

        await _renderSemaphore.WaitAsync(cancellationToken);
        try
        {
            _isInitialized = false;
            _canvas?.Dispose();
            _surface?.Dispose();
            _canvas = null;
            _surface = null;

            _logger.LogInformation("Render engine shutdown completed");
        }
        finally
        {
            _renderSemaphore.Release();
        }
    }

    /// <summary>
    /// Disposes the render engine
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        try
        {
            ShutdownAsync().GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during render engine disposal");
        }
        finally
        {
            _renderSemaphore?.Dispose();
            _disposed = true;
        }
    }

    private void ThrowIfNotInitialized()
    {
        if (!_isInitialized)
            throw new InvalidOperationException("Render engine is not initialized");
    }
}
