Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1

# Solution Folders
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Core Modules", "Core Modules", "{F0000000-0000-0000-0000-000000000000}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Graphics Modules", "Graphics Modules", "{F1111111-1111-1111-1111-111111111111}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "System Modules", "System Modules", "{F222**************-2222-************}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{F333**************-3333-************}"
EndProject

# Core Modules
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ArtDesignFramework.Abstractions", "src\Abstractions\ArtDesignFramework.Abstractions.csproj", "{10101010-1010-1010-1010-101010101010}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ArtDesignFramework.Core", "src\Core\ArtDesignFramework.Core.csproj", "{11111111-1111-1111-1111-111111111111}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ArtDesignFramework.EffectsEngine", "src\EffectsEngine\ArtDesignFramework.EffectsEngine.csproj", "{2222**************-2222-************}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ArtDesignFramework.FontRendering", "src\FontRendering\ArtDesignFramework.FontRendering.csproj", "{3333**************-3333-************}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ArtDesignFramework.ImageHandling", "src\ImageHandling\ArtDesignFramework.ImageHandling.csproj", "{44444444-4444-4444-4444-444444444444}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ArtDesignFramework.Performance", "src\Performance\ArtDesignFramework.Performance.csproj", "{55555555-5555-5555-5555-555555555555}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ArtDesignFramework.PluginSystem", "src\PluginSystem\ArtDesignFramework.PluginSystem.csproj", "{66666666-6666-6666-6666-666666666666}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ArtDesignFramework.TestFramework", "src\TestFramework\ArtDesignFramework.TestFramework.csproj", "{77777777-7777-7777-7777-777777777777}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ArtDesignFramework.ThemingEngine", "src\ThemingEngine\ArtDesignFramework.ThemingEngine.csproj", "{88888888-8888-8888-8888-888888888888}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ArtDesignFramework.UserInterface", "src\UserInterface\ArtDesignFramework.UserInterface.csproj", "{99999999-9999-9999-9999-999999999999}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ArtDesignFramework.Utilities", "src\Utilities\ArtDesignFramework.Utilities.csproj", "{AAAAAAAA-AAAA-AAAA-AAAA-AAAAAAAAAAAA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ArtDesignFramework.VectorGraphics", "src\VectorGraphics\ArtDesignFramework.VectorGraphics.csproj", "{BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ArtDesignFramework.Text3D", "src\Text3D\ArtDesignFramework.Text3D.csproj", "{FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ArtDesignFramework.FreeFonts", "src\FreeFonts\ArtDesignFramework.FreeFonts.csproj", "{11111111-1111-1111-1111-111111111111}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ArtDesignFramework.WebServer", "src\WebServer\ArtDesignFramework.WebServer.csproj", "{2222**************-2222-************}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ArtDesignFramework.DataAccess", "src\DataAccess\ArtDesignFramework.DataAccess.csproj", "{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD}"
EndProject

# Test Projects
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ArtDesignFramework.Core.Tests", "tests\ArtDesignFramework.Core.Tests\ArtDesignFramework.Core.Tests.csproj", "{CCCCCCCC-CCCC-CCCC-CCCC-CCCCCCCCCCCC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ArtDesignFramework.VectorGraphics.Tests", "tests\ArtDesignFramework.VectorGraphics.Tests\ArtDesignFramework.VectorGraphics.Tests.csproj", "{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ArtDesignFramework.PluginSystem.Tests", "tests\ArtDesignFramework.PluginSystem.Tests\ArtDesignFramework.PluginSystem.Tests.csproj", "{EEEEEEEE-EEEE-EEEE-EEEE-EEEEEEEEEEEE}"
EndProject

Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{10101010-1010-1010-1010-101010101010}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{10101010-1010-1010-1010-101010101010}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{10101010-1010-1010-1010-101010101010}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{10101010-1010-1010-1010-101010101010}.Release|Any CPU.Build.0 = Release|Any CPU
		{11111111-1111-1111-1111-111111111111}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{11111111-1111-1111-1111-111111111111}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{11111111-1111-1111-1111-111111111111}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{11111111-1111-1111-1111-111111111111}.Release|Any CPU.Build.0 = Release|Any CPU
		{2222**************-2222-************}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2222**************-2222-************}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2222**************-2222-************}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2222**************-2222-************}.Release|Any CPU.Build.0 = Release|Any CPU
		{3333**************-3333-************}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3333**************-3333-************}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3333**************-3333-************}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3333**************-3333-************}.Release|Any CPU.Build.0 = Release|Any CPU
		{44444444-4444-4444-4444-444444444444}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{44444444-4444-4444-4444-444444444444}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{44444444-4444-4444-4444-444444444444}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{44444444-4444-4444-4444-444444444444}.Release|Any CPU.Build.0 = Release|Any CPU
		{55555555-5555-5555-5555-555555555555}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{55555555-5555-5555-5555-555555555555}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{55555555-5555-5555-5555-555555555555}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{55555555-5555-5555-5555-555555555555}.Release|Any CPU.Build.0 = Release|Any CPU
		{66666666-6666-6666-6666-666666666666}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{66666666-6666-6666-6666-666666666666}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{66666666-6666-6666-6666-666666666666}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{66666666-6666-6666-6666-666666666666}.Release|Any CPU.Build.0 = Release|Any CPU
		{77777777-7777-7777-7777-777777777777}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{77777777-7777-7777-7777-777777777777}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{77777777-7777-7777-7777-777777777777}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{77777777-7777-7777-7777-777777777777}.Release|Any CPU.Build.0 = Release|Any CPU
		{88888888-8888-8888-8888-888888888888}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{88888888-8888-8888-8888-888888888888}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{88888888-8888-8888-8888-888888888888}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{88888888-8888-8888-8888-888888888888}.Release|Any CPU.Build.0 = Release|Any CPU
		{99999999-9999-9999-9999-999999999999}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{99999999-9999-9999-9999-999999999999}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{99999999-9999-9999-9999-999999999999}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{99999999-9999-9999-9999-999999999999}.Release|Any CPU.Build.0 = Release|Any CPU
		{AAAAAAAA-AAAA-AAAA-AAAA-AAAAAAAAAAAA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AAAAAAAA-AAAA-AAAA-AAAA-AAAAAAAAAAAA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AAAAAAAA-AAAA-AAAA-AAAA-AAAAAAAAAAAA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AAAAAAAA-AAAA-AAAA-AAAA-AAAAAAAAAAAA}.Release|Any CPU.Build.0 = Release|Any CPU
		{BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB}.Release|Any CPU.Build.0 = Release|Any CPU
		{FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF}.Release|Any CPU.Build.0 = Release|Any CPU
		{11111111-1111-1111-1111-111111111111}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{11111111-1111-1111-1111-111111111111}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{11111111-1111-1111-1111-111111111111}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{11111111-1111-1111-1111-111111111111}.Release|Any CPU.Build.0 = Release|Any CPU
		{2222**************-2222-************}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2222**************-2222-************}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2222**************-2222-************}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2222**************-2222-************}.Release|Any CPU.Build.0 = Release|Any CPU
		{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD}.Release|Any CPU.Build.0 = Release|Any CPU
		{CCCCCCCC-CCCC-CCCC-CCCC-CCCCCCCCCCCC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CCCCCCCC-CCCC-CCCC-CCCC-CCCCCCCCCCCC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CCCCCCCC-CCCC-CCCC-CCCC-CCCCCCCCCCCC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CCCCCCCC-CCCC-CCCC-CCCC-CCCCCCCCCCCC}.Release|Any CPU.Build.0 = Release|Any CPU
		{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD}.Release|Any CPU.Build.0 = Release|Any CPU
		{EEEEEEEE-EEEE-EEEE-EEEE-EEEEEEEEEEEE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EEEEEEEE-EEEE-EEEE-EEEE-EEEEEEEEEEEE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EEEEEEEE-EEEE-EEEE-EEEE-EEEEEEEEEEEE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EEEEEEEE-EEEE-EEEE-EEEE-EEEEEEEEEEEE}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{10101010-1010-1010-1010-101010101010} = {F0000000-0000-0000-0000-000000000000}
		{11111111-1111-1111-1111-111111111111} = {F0000000-0000-0000-0000-000000000000}
		{77777777-7777-7777-7777-777777777777} = {F0000000-0000-0000-0000-000000000000}
		{AAAAAAAA-AAAA-AAAA-AAAA-AAAAAAAAAAAA} = {F0000000-0000-0000-0000-000000000000}
		{2222**************-2222-************} = {F1111111-1111-1111-1111-111111111111}
		{3333**************-3333-************} = {F1111111-1111-1111-1111-111111111111}
		{44444444-4444-4444-4444-444444444444} = {F1111111-1111-1111-1111-111111111111}
		{88888888-8888-8888-8888-888888888888} = {F1111111-1111-1111-1111-111111111111}
		{99999999-9999-9999-9999-999999999999} = {F1111111-1111-1111-1111-111111111111}
		{BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB} = {F1111111-1111-1111-1111-111111111111}
		{FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF} = {F1111111-1111-1111-1111-111111111111}
		{11111111-1111-1111-1111-111111111111} = {F1111111-1111-1111-1111-111111111111}
		{2222**************-2222-************} = {F1111111-1111-1111-1111-111111111111}
		{55555555-5555-5555-5555-555555555555} = {F222**************-2222-************}
		{66666666-6666-6666-6666-666666666666} = {F222**************-2222-************}
		{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD} = {F222**************-2222-************}
		{CCCCCCCC-CCCC-CCCC-CCCC-CCCCCCCCCCCC} = {F333**************-3333-************}
		{DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD} = {F333**************-3333-************}
		{EEEEEEEE-EEEE-EEEE-EEEE-EEEEEEEEEEEE} = {F333**************-3333-************}
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {12345678-1234-5678-9ABC-123456789ABC}
	EndGlobalSection
EndGlobal
