// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using System.Diagnostics;
using ArtDesignFramework.ClockDesktopApp.Services;
using ArtDesignFramework.Core.Attributes;
using ArtDesignFramework.Performance.Memory;
using ArtDesignFramework.Performance.Monitoring;
using ArtDesignFramework.Performance.Rendering;
using ArtDesignFramework.UserInterface.Services.Rendering;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using SkiaSharp;

namespace ArtDesignFramework.ClockDesktopApp.Tests.Helpers;

/// <summary>
/// Test helper utilities for ClockDesktopApp testing
/// Last Updated: 2025-01-27 22:45:00 UTC
/// </summary>
[Testable("TestInfrastructure", IncludePerformanceTests = true, IncludeIntegrationTests = true)]
public static class TestHelper
{
    /// <summary>
    /// Creates a test service provider with all required dependencies
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [TestableMethod("ServiceProviderCreation", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 100)]
    public static IServiceProvider CreateTestServiceProvider()
    {
        var services = new ServiceCollection();
        
        // Add logging
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
        
        // Add mocked performance services
        services.AddSingleton(CreateMockPerformanceMonitor());
        services.AddSingleton(CreateMockRenderingProfiler());
        services.AddSingleton(CreateMockMemoryProfiler());
        
        // Add real services
        services.AddSingleton<ISKPaintPool>(provider => 
            new SKPaintPool(100, provider.GetRequiredService<ILogger<SKPaintPool>>()));
        services.AddSingleton<EnhancedFontLoadingService>();
        services.AddSingleton<ClockWidgetService>();
        services.AddSingleton<SettingsService>();
        
        return services.BuildServiceProvider();
    }

    /// <summary>
    /// Creates a mock performance monitor for testing
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [TestableMethod("MockPerformanceMonitor", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 50)]
    public static IPerformanceMonitor CreateMockPerformanceMonitor()
    {
        var mock = new Mock<IPerformanceMonitor>();
        var mockMeasurement = new Mock<IPerformanceMeasurement>();
        
        mockMeasurement.Setup(m => m.AddMetadata(It.IsAny<string>(), It.IsAny<object>()));
        mockMeasurement.Setup(m => m.MarkAsFailed(It.IsAny<Exception>()));
        mockMeasurement.Setup(m => m.Dispose());
        
        mock.Setup(m => m.BeginMeasurement(It.IsAny<string>()))
            .Returns(mockMeasurement.Object);
        
        return mock.Object;
    }

    /// <summary>
    /// Creates a mock rendering profiler for testing
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [TestableMethod("MockRenderingProfiler", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 50)]
    public static IRenderingProfiler CreateMockRenderingProfiler()
    {
        var mock = new Mock<IRenderingProfiler>();
        
        mock.Setup(m => m.RecordFrame(It.IsAny<TimeSpan>(), It.IsAny<int>(), It.IsAny<int>()));
        mock.Setup(m => m.GetCurrentMetrics())
            .Returns(new RenderingMetrics { CurrentFps = 60.0, FrameTimeMs = 16.67 });
        
        return mock.Object;
    }

    /// <summary>
    /// Creates a mock memory profiler for testing
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [TestableMethod("MockMemoryProfiler", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 50)]
    public static IMemoryProfiler CreateMockMemoryProfiler()
    {
        var mock = new Mock<IMemoryProfiler>();
        
        mock.Setup(m => m.GetCurrentUsage())
            .Returns(new MemoryUsage 
            { 
                ManagedHeapBytes = 1024 * 1024, // 1MB
                PressureLevel = MemoryPressureLevel.Low 
            });
        
        return mock.Object;
    }

    /// <summary>
    /// Creates test clock settings with default values
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [TestableMethod("TestClockSettings", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 50)]
    public static ClockSettings CreateTestClockSettings()
    {
        return new ClockSettings
        {
            FontFamily = "Arial",
            FontSize = 48,
            TextColor = System.Windows.Media.Brushes.White,
            DateColor = System.Windows.Media.Brushes.LightGray,
            BackgroundColor = System.Windows.Media.Brushes.Black,
            TransparentBackground = false,
            AlwaysOnTop = true,
            ShowDate = true,
            EnableGlow = false,
            EnableShadow = false,
            EnableStroke = false,
            Enable3D = false,
            Width = 800,
            Height = 600,
            Left = 100,
            Top = 100,
            Opacity = 1.0,
            TransparencyLevel = 1.0,
            EnableClickThrough = false,
            UsePerPixelTransparency = true,
            EnableSmoothTransitions = true
        };
    }

    /// <summary>
    /// Measures execution time of an action
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [TestableMethod("ExecutionTimeMeasurement", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 10)]
    public static TimeSpan MeasureExecutionTime(Action action)
    {
        var stopwatch = Stopwatch.StartNew();
        action();
        stopwatch.Stop();
        return stopwatch.Elapsed;
    }

    /// <summary>
    /// Measures execution time of an async action
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [TestableMethod("AsyncExecutionTimeMeasurement", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 10)]
    public static async Task<TimeSpan> MeasureExecutionTimeAsync(Func<Task> action)
    {
        var stopwatch = Stopwatch.StartNew();
        await action();
        stopwatch.Stop();
        return stopwatch.Elapsed;
    }

    /// <summary>
    /// Creates a test SKCanvas for rendering tests
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [TestableMethod("TestCanvasCreation", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 50)]
    public static (SKCanvas canvas, SKBitmap bitmap) CreateTestCanvas(int width = 800, int height = 600)
    {
        var bitmap = new SKBitmap(width, height);
        var canvas = new SKCanvas(bitmap);
        return (canvas, bitmap);
    }

    /// <summary>
    /// Validates that a method has the TestableMethod attribute
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [TestableMethod("TestableMethodValidation", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 50)]
    public static bool HasTestableMethodAttribute(Type type, string methodName)
    {
        var method = type.GetMethod(methodName);
        return method?.GetCustomAttributes(typeof(TestableMethodAttribute), false).Length > 0;
    }

    /// <summary>
    /// Validates performance targets for rendering operations
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [TestableMethod("PerformanceTargetValidation", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 100)]
    public static bool ValidatePerformanceTargets(TimeSpan executionTime, int expectedMaxMs)
    {
        return executionTime.TotalMilliseconds <= expectedMaxMs;
    }

    /// <summary>
    /// Validates 60 FPS rendering capability (16.67ms per frame)
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [TestableMethod("FrameRateValidation", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 50)]
    public static bool Validate60FpsCapability(TimeSpan frameTime)
    {
        return frameTime.TotalMilliseconds <= 16.67; // 60 FPS = 16.67ms per frame
    }

    /// <summary>
    /// Validates memory reduction targets (70% reduction)
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [TestableMethod("MemoryReductionValidation", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 50)]
    public static bool ValidateMemoryReduction(long baselineMemory, long optimizedMemory, double targetReduction = 0.70)
    {
        var actualReduction = 1.0 - ((double)optimizedMemory / baselineMemory);
        return actualReduction >= targetReduction;
    }
}
