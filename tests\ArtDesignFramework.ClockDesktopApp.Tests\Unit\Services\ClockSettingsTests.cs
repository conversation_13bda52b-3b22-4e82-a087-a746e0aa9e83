// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using ArtDesignFramework.ClockDesktopApp.Services;
using ArtDesignFramework.ClockDesktopApp.Tests.Helpers;
using ArtDesignFramework.Core.Attributes;
using FluentAssertions;
using Xunit;

namespace ArtDesignFramework.ClockDesktopApp.Tests.Unit.Services;

/// <summary>
/// Unit tests for ClockSettings service
/// Last Updated: 2025-01-27 22:45:00 UTC
/// </summary>
[Testable("ClockSettings", IncludePerformanceTests = true, IncludeIntegrationTests = true)]
public class ClockSettingsTests
{
    /// <summary>
    /// Tests that default factory settings are created correctly
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [Fact]
    [TestableMethod("DefaultSettingsCreation", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 50)]
    public void CreateFactoryDefaults_ShouldReturnValidSettings()
    {
        // Act
        var executionTime = TestHelper.MeasureExecutionTime(() =>
        {
            var settings = ClockSettings.CreateFactoryDefaults();

            // Assert
            settings.Should().NotBeNull();
            settings.FontFamily.Should().Be("Arial");
            settings.FontSize.Should().Be(72);
            settings.TextColor.Should().NotBeNull();
            settings.DateColor.Should().NotBeNull();
            settings.BackgroundColor.Should().NotBeNull();
            settings.TransparentBackground.Should().BeFalse();
            settings.AlwaysOnTop.Should().BeFalse();
            settings.ShowDate.Should().BeTrue();
            settings.Width.Should().Be(800);
            settings.Height.Should().Be(600);
            settings.Opacity.Should().Be(1.0);

            // Enhanced transparency properties
            settings.TransparencyLevel.Should().Be(1.0);
            settings.EnableClickThrough.Should().BeFalse();
            settings.UsePerPixelTransparency.Should().BeTrue();
            settings.EnableSmoothTransitions.Should().BeTrue();
        });

        // Validate performance target
        TestHelper.ValidatePerformanceTargets(executionTime, 50).Should().BeTrue();
    }

    /// <summary>
    /// Tests transparency level property validation
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [Fact]
    [TestableMethod("TransparencyLevelValidation", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 50)]
    public void TransparencyLevel_ShouldAcceptValidValues()
    {
        // Arrange
        var settings = TestHelper.CreateTestClockSettings();

        // Act & Assert
        var executionTime = TestHelper.MeasureExecutionTime(() =>
        {
            // Test valid values
            settings.TransparencyLevel = 0.0;
            settings.TransparencyLevel.Should().Be(0.0);

            settings.TransparencyLevel = 0.5;
            settings.TransparencyLevel.Should().Be(0.5);

            settings.TransparencyLevel = 1.0;
            settings.TransparencyLevel.Should().Be(1.0);
        });

        // Validate performance target
        TestHelper.ValidatePerformanceTargets(executionTime, 50).Should().BeTrue();
    }

    /// <summary>
    /// Tests click-through functionality property
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [Fact]
    [TestableMethod("ClickThroughProperty", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 50)]
    public void EnableClickThrough_ShouldToggleCorrectly()
    {
        // Arrange
        var settings = TestHelper.CreateTestClockSettings();

        // Act & Assert
        var executionTime = TestHelper.MeasureExecutionTime(() =>
        {
            settings.EnableClickThrough.Should().BeFalse(); // Default

            settings.EnableClickThrough = true;
            settings.EnableClickThrough.Should().BeTrue();

            settings.EnableClickThrough = false;
            settings.EnableClickThrough.Should().BeFalse();
        });

        // Validate performance target
        TestHelper.ValidatePerformanceTargets(executionTime, 50).Should().BeTrue();
    }

    /// <summary>
    /// Tests per-pixel transparency property
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [Fact]
    [TestableMethod("PerPixelTransparencyProperty", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 50)]
    public void UsePerPixelTransparency_ShouldToggleCorrectly()
    {
        // Arrange
        var settings = TestHelper.CreateTestClockSettings();

        // Act & Assert
        var executionTime = TestHelper.MeasureExecutionTime(() =>
        {
            settings.UsePerPixelTransparency.Should().BeTrue(); // Default

            settings.UsePerPixelTransparency = false;
            settings.UsePerPixelTransparency.Should().BeFalse();

            settings.UsePerPixelTransparency = true;
            settings.UsePerPixelTransparency.Should().BeTrue();
        });

        // Validate performance target
        TestHelper.ValidatePerformanceTargets(executionTime, 50).Should().BeTrue();
    }

    /// <summary>
    /// Tests smooth transitions property
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [Fact]
    [TestableMethod("SmoothTransitionsProperty", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 50)]
    public void EnableSmoothTransitions_ShouldToggleCorrectly()
    {
        // Arrange
        var settings = TestHelper.CreateTestClockSettings();

        // Act & Assert
        var executionTime = TestHelper.MeasureExecutionTime(() =>
        {
            settings.EnableSmoothTransitions.Should().BeTrue(); // Default

            settings.EnableSmoothTransitions = false;
            settings.EnableSmoothTransitions.Should().BeFalse();

            settings.EnableSmoothTransitions = true;
            settings.EnableSmoothTransitions.Should().BeTrue();
        });

        // Validate performance target
        TestHelper.ValidatePerformanceTargets(executionTime, 50).Should().BeTrue();
    }

    /// <summary>
    /// Tests font properties validation
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [Fact]
    [TestableMethod("FontPropertiesValidation", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 50)]
    public void FontProperties_ShouldAcceptValidValues()
    {
        // Arrange
        var settings = TestHelper.CreateTestClockSettings();

        // Act & Assert
        var executionTime = TestHelper.MeasureExecutionTime(() =>
        {
            // Test font family
            settings.FontFamily = "Times New Roman";
            settings.FontFamily.Should().Be("Times New Roman");

            // Test font size
            settings.FontSize = 24;
            settings.FontSize.Should().Be(24);

            settings.FontSize = 96;
            settings.FontSize.Should().Be(96);
        });

        // Validate performance target
        TestHelper.ValidatePerformanceTargets(executionTime, 50).Should().BeTrue();
    }

    /// <summary>
    /// Tests window positioning properties
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [Fact]
    [TestableMethod("WindowPositioningProperties", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 50)]
    public void WindowPositioning_ShouldAcceptValidValues()
    {
        // Arrange
        var settings = TestHelper.CreateTestClockSettings();

        // Act & Assert
        var executionTime = TestHelper.MeasureExecutionTime(() =>
        {
            // Test position
            settings.Left = 200;
            settings.Top = 300;
            settings.Left.Should().Be(200);
            settings.Top.Should().Be(300);

            // Test size
            settings.Width = 1024;
            settings.Height = 768;
            settings.Width.Should().Be(1024);
            settings.Height.Should().Be(768);
        });

        // Validate performance target
        TestHelper.ValidatePerformanceTargets(executionTime, 50).Should().BeTrue();
    }

    /// <summary>
    /// Tests effects properties validation
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [Fact]
    [TestableMethod("EffectsPropertiesValidation", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 50)]
    public void EffectsProperties_ShouldToggleCorrectly()
    {
        // Arrange
        var settings = TestHelper.CreateTestClockSettings();

        // Act & Assert
        var executionTime = TestHelper.MeasureExecutionTime(() =>
        {
            // Test glow
            settings.EnableGlow = true;
            settings.EnableGlow.Should().BeTrue();

            // Test shadow
            settings.EnableShadow = true;
            settings.EnableShadow.Should().BeTrue();

            // Test stroke
            settings.EnableStroke = true;
            settings.EnableStroke.Should().BeTrue();

            // Test 3D
            settings.Enable3D = true;
            settings.Enable3D.Should().BeTrue();
        });

        // Validate performance target
        TestHelper.ValidatePerformanceTargets(executionTime, 50).Should().BeTrue();
    }

    /// <summary>
    /// Tests comprehensive settings validation
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [Fact]
    [TestableMethod("ComprehensiveSettingsValidation", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 100)]
    public void ComprehensiveSettings_ShouldMaintainConsistency()
    {
        // Arrange & Act
        var executionTime = TestHelper.MeasureExecutionTime(() =>
        {
            var settings = ClockSettings.CreateFactoryDefaults();

            // Test transparency mode consistency
            settings.TransparentBackground = true;
            settings.EnableClickThrough = true;
            settings.UsePerPixelTransparency = true;

            // Verify transparency settings work together
            settings.TransparentBackground.Should().BeTrue();
            settings.EnableClickThrough.Should().BeTrue();
            settings.UsePerPixelTransparency.Should().BeTrue();

            // Test effects with transparency
            settings.EnableGlow = true;
            settings.EnableShadow = true;
            settings.EnableStroke = true;

            // All effects should be enabled
            settings.EnableGlow.Should().BeTrue();
            settings.EnableShadow.Should().BeTrue();
            settings.EnableStroke.Should().BeTrue();
        });

        // Validate performance target
        TestHelper.ValidatePerformanceTargets(executionTime, 100).Should().BeTrue();
    }
}

/// <summary>
/// Unit tests for ClockWidgetService
/// Last Updated: 2025-01-27 22:45:00 UTC
/// </summary>
[Testable("ClockWidgetService", IncludePerformanceTests = true, IncludeIntegrationTests = true)]
public class ClockWidgetServiceTests : IDisposable
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ClockWidgetService _widgetService;

    public ClockWidgetServiceTests()
    {
        _serviceProvider = TestHelper.CreateTestServiceProvider();
        _widgetService = _serviceProvider.GetRequiredService<ClockWidgetService>();
    }

    /// <summary>
    /// Tests widget service initialization
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [Fact]
    [TestableMethod("WidgetServiceInitialization", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 100)]
    public void Constructor_ShouldInitializeCorrectly()
    {
        // Act & Assert
        var executionTime = TestHelper.MeasureExecutionTime(() =>
        {
            _widgetService.Should().NotBeNull();
            _widgetService.GetActiveWidgets().Should().NotBeNull();
            _widgetService.GetActiveWidgets().Should().BeEmpty();
        });

        // Validate performance target
        TestHelper.ValidatePerformanceTargets(executionTime, 100).Should().BeTrue();
    }

    /// <summary>
    /// Tests getting active widgets when none exist
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [Fact]
    [TestableMethod("GetActiveWidgetsEmpty", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 50)]
    public void GetActiveWidgets_WhenEmpty_ShouldReturnEmptyList()
    {
        // Act & Assert
        var executionTime = TestHelper.MeasureExecutionTime(() =>
        {
            var widgets = _widgetService.GetActiveWidgets();
            widgets.Should().NotBeNull();
            widgets.Should().BeEmpty();
            widgets.Count.Should().Be(0);
        });

        // Validate performance target
        TestHelper.ValidatePerformanceTargets(executionTime, 50).Should().BeTrue();
    }

    /// <summary>
    /// Tests closing all widgets when none exist
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [Fact]
    [TestableMethod("CloseAllWidgetsEmpty", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 100)]
    public void CloseAllWidgets_WhenEmpty_ShouldNotThrow()
    {
        // Act & Assert
        var executionTime = TestHelper.MeasureExecutionTime(() =>
        {
            Action closeAction = () => _widgetService.CloseAllWidgets();
            closeAction.Should().NotThrow();
        });

        // Validate performance target
        TestHelper.ValidatePerformanceTargets(executionTime, 100).Should().BeTrue();
    }

    /// <summary>
    /// Tests updating all widgets when none exist
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [Fact]
    [TestableMethod("UpdateAllWidgetsEmpty", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 100)]
    public void UpdateAllWidgets_WhenEmpty_ShouldNotThrow()
    {
        // Arrange
        var settings = TestHelper.CreateTestClockSettings();

        // Act & Assert
        var executionTime = TestHelper.MeasureExecutionTime(() =>
        {
            Action updateAction = () => _widgetService.UpdateAllWidgets(settings);
            updateAction.Should().NotThrow();
        });

        // Validate performance target
        TestHelper.ValidatePerformanceTargets(executionTime, 100).Should().BeTrue();
    }

    /// <summary>
    /// Tests widget service with null settings
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [Fact]
    [TestableMethod("NullSettingsHandling", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 50)]
    public void UpdateAllWidgets_WithNullSettings_ShouldHandleGracefully()
    {
        // Act & Assert
        var executionTime = TestHelper.MeasureExecutionTime(() =>
        {
            Action updateAction = () => _widgetService.UpdateAllWidgets(null!);
            // Should handle null gracefully or throw appropriate exception
            updateAction.Should().NotThrow();
        });

        // Validate performance target
        TestHelper.ValidatePerformanceTargets(executionTime, 50).Should().BeTrue();
    }

    public void Dispose()
    {
        _widgetService?.CloseAllWidgets();
        (_serviceProvider as IDisposable)?.Dispose();
    }
}
