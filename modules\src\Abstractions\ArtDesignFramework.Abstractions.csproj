<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <!-- Abstractions module specific settings -->
    <AssemblyTitle>ArtDesignFramework Abstractions Module</AssemblyTitle>
    <AssemblyDescription>Shared abstractions, interfaces, and attributes for the ArtDesignFramework</AssemblyDescription>
  </PropertyGroup>

  <!-- No additional package references needed - only System.* references -->
  <!-- This module provides shared contracts and should have minimal dependencies -->

</Project>
