// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using System.Collections.Concurrent;
using ArtDesignFramework.Abstractions;
using Microsoft.Extensions.Logging;

namespace ArtDesignFramework.Core;

/// <summary>
/// Manages layers in the design framework
/// </summary>
[Testable]
public sealed class LayerManager : ILayerManager
{
    private readonly ILogger<LayerManager> _logger;
    private readonly ConcurrentBag<ILayer> _layers = new();
    private readonly ReaderWriterLockSlim _layersLock = new();
    private volatile bool _disposed;

    /// <summary>
    /// Initializes a new instance of the <see cref="LayerManager"/> class
    /// </summary>
    /// <param name="logger">Logger instance</param>
    public LayerManager(ILogger<LayerManager> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Gets all layers
    /// </summary>
    public IReadOnlyList<ILayer> Layers
    {
        get
        {
            _layersLock.EnterReadLock();
            try
            {
                return _layers.OrderBy(l => l.ZIndex).ToList().AsReadOnly();
            }
            finally
            {
                _layersLock.ExitReadLock();
            }
        }
    }

    /// <summary>
    /// Adds a layer
    /// </summary>
    /// <param name="layer">Layer to add</param>
    public void AddLayer(ILayer layer)
    {
        ArgumentNullException.ThrowIfNull(layer);
        ThrowIfDisposed();

        _layersLock.EnterWriteLock();
        try
        {
            _layers.Add(layer);
            _logger.LogInformation("Layer added: {LayerName} (ID: {LayerId})", layer.Name, layer.Id);
        }
        finally
        {
            _layersLock.ExitWriteLock();
        }
    }

    /// <summary>
    /// Removes a layer
    /// </summary>
    /// <param name="layer">Layer to remove</param>
    /// <returns>True if the layer was removed, false otherwise</returns>
    public bool RemoveLayer(ILayer layer)
    {
        ArgumentNullException.ThrowIfNull(layer);
        ThrowIfDisposed();

        _layersLock.EnterWriteLock();
        try
        {
            var layersList = _layers.ToList();
            var layerToRemove = layersList.FirstOrDefault(l => l.Id == layer.Id);

            if (layerToRemove != null)
            {
                // Recreate the collection without the removed layer
                var newLayers = layersList.Where(l => l.Id != layer.Id);
                _layers.Clear();
                foreach (var remainingLayer in newLayers)
                {
                    _layers.Add(remainingLayer);
                }

                _logger.LogInformation("Layer removed: {LayerName} (ID: {LayerId})", layer.Name, layer.Id);
                return true;
            }

            return false;
        }
        finally
        {
            _layersLock.ExitWriteLock();
        }
    }

    /// <summary>
    /// Clears all layers
    /// </summary>
    public void ClearLayers()
    {
        ThrowIfDisposed();

        _layersLock.EnterWriteLock();
        try
        {
            var count = _layers.Count;
            _layers.Clear();
            _logger.LogInformation("Cleared {LayerCount} layers", count);
        }
        finally
        {
            _layersLock.ExitWriteLock();
        }
    }

    /// <summary>
    /// Disposes the layer manager
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        try
        {
            ClearLayers();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during layer manager disposal");
        }
        finally
        {
            _layersLock?.Dispose();
            _disposed = true;
        }
    }

    private void ThrowIfDisposed()
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(LayerManager));
    }
}
