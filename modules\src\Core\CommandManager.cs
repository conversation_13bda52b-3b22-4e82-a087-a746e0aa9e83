// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using System.Collections.Concurrent;
using ArtDesignFramework.Abstractions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace ArtDesignFramework.Core;

/// <summary>
/// Manages command execution and undo/redo operations with async support
/// </summary>
[Testable]
public sealed class CommandManager : ICommandManager
{
    private readonly ILogger<CommandManager> _logger;
    private readonly CoreOptions _options;
    private readonly ConcurrentStack<ICommand> _undoStack = new();
    private readonly ConcurrentStack<ICommand> _redoStack = new();
    private readonly SemaphoreSlim _executionSemaphore = new(1, 1);
    private volatile bool _disposed;

    /// <summary>
    /// Initializes a new instance of the <see cref="CommandManager"/> class
    /// </summary>
    /// <param name="logger">Logger instance</param>
    /// <param name="options">Core options</param>
    public CommandManager(ILogger<CommandManager> logger, IOptions<CoreOptions> options)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
    }

    /// <summary>
    /// Gets whether undo is available
    /// </summary>
    public bool CanUndo => !_undoStack.IsEmpty;

    /// <summary>
    /// Gets whether redo is available
    /// </summary>
    public bool CanRedo => !_redoStack.IsEmpty;

    /// <summary>
    /// Executes a command asynchronously
    /// </summary>
    /// <param name="command">Command to execute</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the command execution</returns>
    public async Task ExecuteAsync(ICommand command, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(command);
        ThrowIfDisposed();

        await _executionSemaphore.WaitAsync(cancellationToken);
        try
        {
            _logger.LogDebug("Executing command: {CommandName}", command.Name);

            // Execute the command
            await command.ExecuteAsync(cancellationToken);

            // Add to undo stack if the command can be undone
            if (command.CanUndo)
            {
                _undoStack.Push(command);

                // Limit undo stack size
                if (_undoStack.Count > _options.MaxUndoOperations)
                {
                    var commands = _undoStack.ToArray();
                    _undoStack.Clear();

                    // Keep only the most recent commands
                    for (int i = 0; i < _options.MaxUndoOperations; i++)
                    {
                        _undoStack.Push(commands[i]);
                    }
                }

                // Clear redo stack when new command is executed
                ClearRedoStack();
            }

            _logger.LogInformation("Command executed successfully: {CommandName}", command.Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to execute command: {CommandName}", command.Name);
            throw;
        }
        finally
        {
            _executionSemaphore.Release();
        }
    }

    /// <summary>
    /// Undoes the last command
    /// </summary>
    /// <returns>True if a command was undone, false otherwise</returns>
    public bool Undo()
    {
        ThrowIfDisposed();

        if (!_undoStack.TryPop(out var command))
        {
            _logger.LogDebug("No commands available to undo");
            return false;
        }

        try
        {
            _logger.LogDebug("Undoing command: {CommandName}", command.Name);

            // Execute undo asynchronously but wait for completion
            command.UndoAsync().GetAwaiter().GetResult();

            // Move command to redo stack
            _redoStack.Push(command);

            _logger.LogInformation("Command undone successfully: {CommandName}", command.Name);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to undo command: {CommandName}", command.Name);

            // Put the command back on the undo stack
            _undoStack.Push(command);
            return false;
        }
    }

    /// <summary>
    /// Redoes the last undone command
    /// </summary>
    /// <returns>True if a command was redone, false otherwise</returns>
    public bool Redo()
    {
        ThrowIfDisposed();

        if (!_redoStack.TryPop(out var command))
        {
            _logger.LogDebug("No commands available to redo");
            return false;
        }

        try
        {
            _logger.LogDebug("Redoing command: {CommandName}", command.Name);

            // Execute command again asynchronously but wait for completion
            command.ExecuteAsync().GetAwaiter().GetResult();

            // Move command back to undo stack
            _undoStack.Push(command);

            _logger.LogInformation("Command redone successfully: {CommandName}", command.Name);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to redo command: {CommandName}", command.Name);

            // Put the command back on the redo stack
            _redoStack.Push(command);
            return false;
        }
    }

    private void ClearRedoStack()
    {
        while (_redoStack.TryPop(out var command))
        {
            try
            {
                if (command is IDisposable disposable)
                    disposable.Dispose();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error disposing command: {CommandName}", command.Name);
            }
        }
    }

    /// <summary>
    /// Disposes the command manager
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        try
        {
            // Clear both stacks
            while (_undoStack.TryPop(out var command))
            {
                if (command is IDisposable disposable)
                    disposable.Dispose();
            }
            ClearRedoStack();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during command manager disposal");
        }
        finally
        {
            _executionSemaphore?.Dispose();
            _disposed = true;
        }

        _logger.LogInformation("Command manager disposed");
    }

    private void ThrowIfDisposed()
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(CommandManager));
    }
}
