using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace ArtDesignFramework.Abstractions
{
    /// <summary>
    /// Core interface for all framework abilities.
    /// Provides foundation for the registry system with lifecycle management,
    /// metadata, and dependency tracking.
    /// Last Updated: 2025-01-11 15:30:00 UTC
    /// </summary>
    public interface IAbility
    {
        /// <summary>
        /// Gets the unique identifier for this ability.
        /// </summary>
        string Id { get; }

        /// <summary>
        /// Gets the human-readable name of the ability.
        /// </summary>
        string Name { get; }

        /// <summary>
        /// Gets the description of what this ability provides.
        /// </summary>
        string Description { get; }

        /// <summary>
        /// Gets the version of this ability implementation.
        /// </summary>
        Version Version { get; }

        /// <summary>
        /// Gets the category this ability belongs to.
        /// </summary>
        AbilityCategory Category { get; }

        /// <summary>
        /// Gets the collection of tags for ability discovery.
        /// </summary>
        IReadOnlyCollection<string> Tags { get; }

        /// <summary>
        /// Gets the metadata associated with this ability.
        /// </summary>
        IAbilityMetadata Metadata { get; }

        /// <summary>
        /// Gets the current status of the ability.
        /// </summary>
        AbilityStatus Status { get; }

        /// <summary>
        /// Gets the dependencies required by this ability.
        /// </summary>
        IReadOnlyCollection<AbilityDependency> Dependencies { get; }

        /// <summary>
        /// Gets a value indicating whether this ability is currently active.
        /// </summary>
        bool IsActive { get; }

        /// <summary>
        /// Gets a value indicating whether this ability supports hot-swapping.
        /// </summary>
        bool SupportsHotSwap { get; }

        /// <summary>
        /// Event raised when the ability status changes.
        /// </summary>
        event EventHandler<AbilityStatusChangedEventArgs> StatusChanged;

        /// <summary>
        /// Initializes the ability with the provided context.
        /// </summary>
        /// <param name="context">The initialization context.</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>A task representing the initialization operation.</returns>
        Task<bool> InitializeAsync(IAbilityContext context, CancellationToken cancellationToken = default);

        /// <summary>
        /// Activates the ability for use.
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>A task representing the activation operation.</returns>
        Task<bool> ActivateAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Deactivates the ability.
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>A task representing the deactivation operation.</returns>
        Task<bool> DeactivateAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Validates that this ability is compatible with the specified dependencies.
        /// </summary>
        /// <param name="availableAbilities">The currently available abilities.</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>A task representing the validation result.</returns>
        Task<AbilityValidationResult> ValidateCompatibilityAsync(
            IReadOnlyDictionary<string, IAbility> availableAbilities,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Disposes of the ability and releases resources.
        /// </summary>
        /// <returns>A task representing the disposal operation.</returns>
        Task DisposeAsync();
    }

    /// <summary>
    /// Metadata information for abilities.
    /// Last Updated: 2025-01-11 15:30:00 UTC
    /// </summary>
    public interface IAbilityMetadata
    {
        /// <summary>
        /// Gets the author of the ability.
        /// </summary>
        string Author { get; }

        /// <summary>
        /// Gets the creation date of the ability.
        /// </summary>
        DateTime CreatedDate { get; }

        /// <summary>
        /// Gets the last modified date of the ability.
        /// </summary>
        DateTime LastModified { get; }

        /// <summary>
        /// Gets the license information for the ability.
        /// </summary>
        string License { get; }

        /// <summary>
        /// Gets the minimum framework version required.
        /// </summary>
        Version MinFrameworkVersion { get; }

        /// <summary>
        /// Gets the maximum framework version supported.
        /// </summary>
        Version? MaxFrameworkVersion { get; }

        /// <summary>
        /// Gets additional custom metadata properties.
        /// </summary>
        IReadOnlyDictionary<string, object> CustomProperties { get; }

        /// <summary>
        /// Gets the dependencies required by this ability.
        /// </summary>
        IReadOnlyCollection<AbilityDependency> Dependencies { get; }

        /// <summary>
        /// Gets the performance characteristics of this ability.
        /// </summary>
        AbilityPerformanceInfo PerformanceInfo { get; }
    }

    /// <summary>
    /// Registry interface for managing abilities.
    /// Last Updated: 2025-01-11 15:30:00 UTC
    /// </summary>
    public interface IAbilityRegistry : IDisposable
    {
        /// <summary>
        /// Gets a value indicating whether the registry is initialized.
        /// </summary>
        bool IsInitialized { get; }

        /// <summary>
        /// Gets the count of registered abilities.
        /// </summary>
        int AbilityCount { get; }

        /// <summary>
        /// Event raised when an ability status changes.
        /// </summary>
        event EventHandler<AbilityStatusChangedEventArgs> AbilityStatusChanged;

        /// <summary>
        /// Event raised when an ability is registered.
        /// </summary>
        event EventHandler<AbilityRegisteredEventArgs> AbilityRegistered;

        /// <summary>
        /// Registers an ability with the registry.
        /// </summary>
        /// <param name="ability">The ability to register.</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>A task representing the registration operation.</returns>
        Task<bool> RegisterAbilityAsync(IAbility ability, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets an ability by its identifier.
        /// </summary>
        /// <param name="abilityId">The ability identifier.</param>
        /// <returns>The ability if found; otherwise, null.</returns>
        IAbility? GetAbility(string abilityId);

        /// <summary>
        /// Gets all registered abilities.
        /// </summary>
        /// <returns>A collection of all registered abilities.</returns>
        IReadOnlyCollection<IAbility> GetAllAbilities();

        /// <summary>
        /// Initializes the registry and all registered abilities.
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>A task representing the initialization operation.</returns>
        Task<bool> InitializeAsync(CancellationToken cancellationToken = default);
    }
}
