// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using ArtDesignFramework.Abstractions;

namespace ArtDesignFramework.Core;

/// <summary>
/// Base implementation of a layer
/// </summary>
[Testable]
public class Layer : ILayer
{
    /// <summary>
    /// Initializes a new instance of the <see cref="Layer"/> class
    /// </summary>
    /// <param name="name">Layer name</param>
    public Layer(string name)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(name);

        Id = Guid.NewGuid();
        Name = name;
        IsVisible = true;
        Opacity = 1.0;
        ZIndex = 0;
    }

    /// <summary>
    /// Gets the layer identifier
    /// </summary>
    public Guid Id { get; }

    /// <summary>
    /// Gets or sets the layer name
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// Gets or sets whether the layer is visible
    /// </summary>
    public bool IsVisible { get; set; }

    /// <summary>
    /// Gets or sets the layer opacity (0.0 to 1.0)
    /// </summary>
    public double Opacity
    {
        get => _opacity;
        set => _opacity = Math.Clamp(value, 0.0, 1.0);
    }
    private double _opacity;

    /// <summary>
    /// Gets or sets the layer z-index
    /// </summary>
    public int ZIndex { get; set; }

    /// <summary>
    /// Returns a string representation of the layer
    /// </summary>
    /// <returns>String representation</returns>
    public override string ToString()
    {
        return $"Layer '{Name}' (ID: {Id}, ZIndex: {ZIndex}, Visible: {IsVisible}, Opacity: {Opacity:F2})";
    }

    /// <summary>
    /// Determines whether the specified object is equal to the current layer
    /// </summary>
    /// <param name="obj">Object to compare</param>
    /// <returns>True if equal, false otherwise</returns>
    public override bool Equals(object? obj)
    {
        return obj is ILayer other && Id.Equals(other.Id);
    }

    /// <summary>
    /// Returns a hash code for the layer
    /// </summary>
    /// <returns>Hash code</returns>
    public override int GetHashCode()
    {
        return Id.GetHashCode();
    }
}

/// <summary>
/// Specialized layer for vector graphics
/// </summary>
[Testable]
public class VectorLayer : Layer
{
    /// <summary>
    /// Initializes a new instance of the <see cref="VectorLayer"/> class
    /// </summary>
    /// <param name="name">Layer name</param>
    public VectorLayer(string name) : base(name)
    {
        Shapes = new List<object>();
    }

    /// <summary>
    /// Gets the shapes in this layer
    /// </summary>
    public List<object> Shapes { get; }

    /// <summary>
    /// Adds a shape to the layer
    /// </summary>
    /// <param name="shape">Shape to add</param>
    public void AddShape(object shape)
    {
        ArgumentNullException.ThrowIfNull(shape);
        Shapes.Add(shape);
    }

    /// <summary>
    /// Removes a shape from the layer
    /// </summary>
    /// <param name="shape">Shape to remove</param>
    /// <returns>True if removed, false otherwise</returns>
    public bool RemoveShape(object shape)
    {
        ArgumentNullException.ThrowIfNull(shape);
        return Shapes.Remove(shape);
    }
}

/// <summary>
/// Specialized layer for raster images
/// </summary>
[Testable]
public class ImageLayer : Layer
{
    /// <summary>
    /// Initializes a new instance of the <see cref="ImageLayer"/> class
    /// </summary>
    /// <param name="name">Layer name</param>
    /// <param name="imagePath">Path to the image file</param>
    public ImageLayer(string name, string imagePath) : base(name)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(imagePath);
        ImagePath = imagePath;
    }

    /// <summary>
    /// Gets the path to the image file
    /// </summary>
    public string ImagePath { get; }

    /// <summary>
    /// Gets or sets the image transformation matrix
    /// </summary>
    public object? Transform { get; set; }
}

/// <summary>
/// Specialized layer for text content
/// </summary>
[Testable]
public class TextLayer : Layer
{
    /// <summary>
    /// Initializes a new instance of the <see cref="TextLayer"/> class
    /// </summary>
    /// <param name="name">Layer name</param>
    /// <param name="text">Text content</param>
    public TextLayer(string name, string text) : base(name)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(text);
        Text = text;
        FontSize = 12;
        FontFamily = "Arial";
    }

    /// <summary>
    /// Gets or sets the text content
    /// </summary>
    public string Text { get; set; }

    /// <summary>
    /// Gets or sets the font size
    /// </summary>
    public double FontSize { get; set; }

    /// <summary>
    /// Gets or sets the font family
    /// </summary>
    public string FontFamily { get; set; }

    /// <summary>
    /// Gets or sets the text color
    /// </summary>
    public object? Color { get; set; }
}
