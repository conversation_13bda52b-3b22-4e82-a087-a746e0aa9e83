// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using ArtDesignFramework.ClockDesktopApp.Services;
using ArtDesignFramework.ClockDesktopApp.Tests.Helpers;
using ArtDesignFramework.Core.Attributes;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Xunit;

namespace ArtDesignFramework.ClockDesktopApp.Tests.Integration;

/// <summary>
/// Integration tests for ClockDesktopApp module functionality
/// Last Updated: 2025-01-27 22:45:00 UTC
/// </summary>
[Testable("ClockDesktopAppIntegration", IncludePerformanceTests = true, IncludeIntegrationTests = true)]
public class ClockDesktopAppIntegrationTests : IDisposable
{
    private readonly IServiceProvider _serviceProvider;

    public ClockDesktopAppIntegrationTests()
    {
        _serviceProvider = TestHelper.CreateTestServiceProvider();
    }

    /// <summary>
    /// Tests transparency functionality integration
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [Fact]
    [TestableMethod("TransparencyFunctionalityIntegration", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 200)]
    public void TransparencyFunctionality_ShouldIntegrateCorrectly()
    {
        // Arrange
        var settings = TestHelper.CreateTestClockSettings();
        
        // Act & Assert
        var executionTime = TestHelper.MeasureExecutionTime(() =>
        {
            // Test transparency mode activation
            settings.TransparentBackground = true;
            settings.EnableClickThrough = true;
            settings.UsePerPixelTransparency = true;
            settings.TransparencyLevel = 0.5;

            // Verify transparency settings work together
            settings.TransparentBackground.Should().BeTrue();
            settings.EnableClickThrough.Should().BeTrue();
            settings.UsePerPixelTransparency.Should().BeTrue();
            settings.TransparencyLevel.Should().Be(0.5);

            // Test smooth transitions
            settings.EnableSmoothTransitions = true;
            settings.EnableSmoothTransitions.Should().BeTrue();
        });

        // Validate performance target
        TestHelper.ValidatePerformanceTargets(executionTime, 200).Should().BeTrue();
    }

    /// <summary>
    /// Tests font loading reliability integration
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [Fact]
    [TestableMethod("FontLoadingReliabilityIntegration", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 2000)]
    public async Task FontLoadingReliability_ShouldIntegrateCorrectly()
    {
        // Arrange
        var fontService = _serviceProvider.GetRequiredService<EnhancedFontLoadingService>();

        // Act & Assert
        var executionTime = await TestHelper.MeasureExecutionTimeAsync(async () =>
        {
            // Test basic font loading
            var fonts = await fontService.GetAvailableFontsAsync();
            fonts.Should().NotBeNull();
            fonts.Should().NotBeEmpty();

            // Test system fonts are available
            fonts.Should().Contain("Arial");
            
            // Test font loading doesn't throw
            Action fontLoadAction = () => fontService.GetAvailableFontsAsync().Wait();
            fontLoadAction.Should().NotThrow();
        });

        // Validate performance target
        TestHelper.ValidatePerformanceTargets(executionTime, 2000).Should().BeTrue();
    }

    /// <summary>
    /// Tests settings storage and retrieval integration
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [Fact]
    [TestableMethod("SettingsStorageIntegration", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 500)]
    public async Task SettingsStorage_ShouldIntegrateCorrectly()
    {
        // Arrange
        var settingsService = _serviceProvider.GetRequiredService<SettingsService>();
        var testSettings = TestHelper.CreateTestClockSettings();
        testSettings.FontFamily = "Test Font";
        testSettings.FontSize = 99;

        // Act & Assert
        var executionTime = await TestHelper.MeasureExecutionTimeAsync(async () =>
        {
            // Test saving settings
            await settingsService.SaveSettingsAsync(testSettings);

            // Test loading settings
            var loadedSettings = await settingsService.LoadSettingsAsync();
            loadedSettings.Should().NotBeNull();
            
            // Verify settings persistence (may use fallback storage)
            Action saveAction = () => settingsService.SaveSettingsAsync(testSettings).Wait();
            saveAction.Should().NotThrow();
            
            Action loadAction = () => settingsService.LoadSettingsAsync().Wait();
            loadAction.Should().NotThrow();
        });

        // Validate performance target
        TestHelper.ValidatePerformanceTargets(executionTime, 500).Should().BeTrue();
    }

    /// <summary>
    /// Tests server/file storage fallback integration
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [Fact]
    [TestableMethod("StorageFallbackIntegration", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 1000)]
    public async Task StorageFallback_ShouldIntegrateCorrectly()
    {
        // Arrange
        var settingsService = _serviceProvider.GetRequiredService<SettingsService>();
        var testSettings = TestHelper.CreateTestClockSettings();

        // Act & Assert
        var executionTime = await TestHelper.MeasureExecutionTimeAsync(async () =>
        {
            // Test that storage operations don't fail even if server is unavailable
            Action saveAction = () => settingsService.SaveSettingsAsync(testSettings).Wait();
            saveAction.Should().NotThrow("Storage should fallback gracefully");

            Action loadAction = () => settingsService.LoadSettingsAsync().Wait();
            loadAction.Should().NotThrow("Storage should fallback gracefully");

            // Verify fallback mechanism works
            var loadedSettings = await settingsService.LoadSettingsAsync();
            loadedSettings.Should().NotBeNull("Should return default settings if storage fails");
        });

        // Validate performance target
        TestHelper.ValidatePerformanceTargets(executionTime, 1000).Should().BeTrue();
    }

    /// <summary>
    /// Tests widget functionality in both workshop and desktop modes
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [Fact]
    [TestableMethod("WidgetModesIntegration", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 500)]
    public void WidgetModes_ShouldIntegrateCorrectly()
    {
        // Arrange
        var widgetService = _serviceProvider.GetRequiredService<ClockWidgetService>();
        var settings = TestHelper.CreateTestClockSettings();

        // Act & Assert
        var executionTime = TestHelper.MeasureExecutionTime(() =>
        {
            // Test widget service initialization
            widgetService.Should().NotBeNull();
            widgetService.GetActiveWidgets().Should().NotBeNull();

            // Test widget management operations
            Action closeAction = () => widgetService.CloseAllWidgets();
            closeAction.Should().NotThrow();

            Action updateAction = () => widgetService.UpdateAllWidgets(settings);
            updateAction.Should().NotThrow();

            // Verify widget service state
            var widgets = widgetService.GetActiveWidgets();
            widgets.Should().NotBeNull();
        });

        // Validate performance target
        TestHelper.ValidatePerformanceTargets(executionTime, 500).Should().BeTrue();
    }

    /// <summary>
    /// Tests performance monitoring integration
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [Fact]
    [TestableMethod("PerformanceMonitoringIntegration", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 200)]
    public void PerformanceMonitoring_ShouldIntegrateCorrectly()
    {
        // Arrange
        var performanceMonitor = TestHelper.CreateMockPerformanceMonitor();
        var renderingProfiler = TestHelper.CreateMockRenderingProfiler();
        var memoryProfiler = TestHelper.CreateMockMemoryProfiler();

        // Act & Assert
        var executionTime = TestHelper.MeasureExecutionTime(() =>
        {
            // Test performance services are available
            performanceMonitor.Should().NotBeNull();
            renderingProfiler.Should().NotBeNull();
            memoryProfiler.Should().NotBeNull();

            // Test performance measurement creation
            using var measurement = performanceMonitor.BeginMeasurement("TestOperation");
            measurement.Should().NotBeNull();

            // Test metadata addition
            Action metadataAction = () => measurement.AddMetadata("TestKey", "TestValue");
            metadataAction.Should().NotThrow();

            // Test rendering metrics
            var metrics = renderingProfiler.GetCurrentMetrics();
            metrics.Should().NotBeNull();
            metrics.CurrentFps.Should().BeGreaterThan(0);

            // Test memory usage
            var memoryUsage = memoryProfiler.GetCurrentUsage();
            memoryUsage.Should().NotBeNull();
            memoryUsage.ManagedHeapBytes.Should().BeGreaterThan(0);
        });

        // Validate performance target
        TestHelper.ValidatePerformanceTargets(executionTime, 200).Should().BeTrue();
    }

    /// <summary>
    /// Tests comprehensive module integration
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [Fact]
    [TestableMethod("ComprehensiveModuleIntegration", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 1000)]
    public async Task ComprehensiveModule_ShouldIntegrateCorrectly()
    {
        // Arrange
        var settings = TestHelper.CreateTestClockSettings();
        var widgetService = _serviceProvider.GetRequiredService<ClockWidgetService>();
        var settingsService = _serviceProvider.GetRequiredService<SettingsService>();
        var fontService = _serviceProvider.GetRequiredService<EnhancedFontLoadingService>();

        // Act & Assert
        var executionTime = await TestHelper.MeasureExecutionTimeAsync(async () =>
        {
            // Test complete workflow integration
            
            // 1. Font loading
            var fonts = await fontService.GetAvailableFontsAsync();
            fonts.Should().NotBeEmpty();

            // 2. Settings management
            await settingsService.SaveSettingsAsync(settings);
            var loadedSettings = await settingsService.LoadSettingsAsync();
            loadedSettings.Should().NotBeNull();

            // 3. Widget management
            widgetService.UpdateAllWidgets(settings);
            var widgets = widgetService.GetActiveWidgets();
            widgets.Should().NotBeNull();

            // 4. Transparency functionality
            settings.TransparentBackground = true;
            settings.EnableClickThrough = true;
            settings.UsePerPixelTransparency = true;
            
            // All operations should complete without errors
            settings.TransparentBackground.Should().BeTrue();
            settings.EnableClickThrough.Should().BeTrue();
            settings.UsePerPixelTransparency.Should().BeTrue();
        });

        // Validate performance target
        TestHelper.ValidatePerformanceTargets(executionTime, 1000).Should().BeTrue();
    }

    public void Dispose()
    {
        (_serviceProvider as IDisposable)?.Dispose();
    }
}
