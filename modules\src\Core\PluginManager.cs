// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using ArtDesignFramework.Abstractions;

namespace ArtDesignFramework.Core;

/// <summary>
/// Manages plugins in the framework
/// </summary>
[Testable]
public sealed class PluginManager : IDisposable
{
    private readonly Dictionary<string, object> _plugins = new();
    private bool _disposed;

    /// <summary>
    /// Gets all loaded plugins
    /// </summary>
    public IReadOnlyDictionary<string, object> Plugins => _plugins.AsReadOnly();

    /// <summary>
    /// Loads a plugin
    /// </summary>
    /// <param name="pluginId">Plugin identifier</param>
    /// <param name="plugin">Plugin instance</param>
    public void LoadPlugin(string pluginId, object plugin)
    {
        ThrowIfDisposed();
        _plugins[pluginId] = plugin;
        // TODO: Add proper logging via dependency injection
        Console.WriteLine($"Plugin loaded: {pluginId}. Total plugins: {_plugins.Count}");
    }

    /// <summary>
    /// Unloads a plugin
    /// </summary>
    /// <param name="pluginId">Plugin identifier</param>
    /// <returns>True if the plugin was unloaded</returns>
    public bool UnloadPlugin(string pluginId)
    {
        ThrowIfDisposed();
        var removed = _plugins.Remove(pluginId);
        if (removed)
        {
            // TODO: Add proper logging via dependency injection
            Console.WriteLine($"Plugin unloaded: {pluginId}. Total plugins: {_plugins.Count}");
        }
        return removed;
    }

    /// <summary>
    /// Gets a plugin by identifier
    /// </summary>
    /// <param name="pluginId">Plugin identifier</param>
    /// <returns>Plugin instance or null if not found</returns>
    public object? GetPlugin(string pluginId)
    {
        ThrowIfDisposed();
        return _plugins.TryGetValue(pluginId, out var plugin) ? plugin : null;
    }

    /// <summary>
    /// Unloads all plugins
    /// </summary>
    public void UnloadAllPlugins()
    {
        ThrowIfDisposed();
        _plugins.Clear();
        // TODO: Add proper logging via dependency injection
        Console.WriteLine("All plugins unloaded");
    }

    /// <summary>
    /// Disposes the plugin manager
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            UnloadAllPlugins();
            _disposed = true;
        }
    }

    private void ThrowIfDisposed()
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(PluginManager));
    }
}
