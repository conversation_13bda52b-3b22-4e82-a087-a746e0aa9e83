@echo off
echo ========================================
echo    ArtDesignFramework Clock Desktop App
echo ========================================
echo.
echo Starting the Clock Desktop Application...
echo.

cd /d "L:\Framework\modules\src\ClockDesktopApp\bin\Debug\net9.0-windows"

if exist "ArtDesignFramework.ClockDesktopApp.exe" (
    echo Found executable: ArtDesignFramework.ClockDesktopApp.exe
    echo Launching Clock Desktop App...
    echo.
    start "" "ArtDesignFramework.ClockDesktopApp.exe"
    echo.
    echo Clock Desktop App launched successfully!
    echo.
    echo Features available:
    echo - Clock Workshop: Customize your clock settings
    echo - Desktop Widgets: Create draggable desktop clocks
    echo - Transparency: Advanced transparency and click-through
    echo - Effects: Glow, shadow, stroke, and 3D text effects
    echo - Performance: 60 FPS rendering with memory optimization
    echo.
    echo Press any key to exit this launcher...
    pause >nul
) else (
    echo ERROR: ArtDesignFramework.ClockDesktopApp.exe not found!
    echo.
    echo Please ensure the application has been built successfully.
    echo Try running: dotnet build modules/src/ClockDesktopApp
    echo.
    pause
)
