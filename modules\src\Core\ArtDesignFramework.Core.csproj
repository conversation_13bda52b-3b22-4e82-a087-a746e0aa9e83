<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <!-- Core module specific settings -->
    <AssemblyTitle>ArtDesignFramework Core Module</AssemblyTitle>
    <AssemblyDescription>Core functionality and base classes for the ArtDesignFramework</AssemblyDescription>
  </PropertyGroup>

  <ItemGroup>
    <!-- Additional packages not included in Directory.Build.props -->
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <!-- Project references -->
    <ProjectReference Include="..\Abstractions\ArtDesignFramework.Abstractions.csproj" />
  </ItemGroup>

</Project>
