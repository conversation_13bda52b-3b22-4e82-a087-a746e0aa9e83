﻿// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using ArtDesignFramework.Abstractions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace ArtDesignFramework.Core;

/// <summary>
/// Core framework service providing centralized access to framework components
/// </summary>
public interface IFrameworkService
{
    /// <summary>
    /// Gets whether the framework is initialized
    /// </summary>
    bool IsInitialized { get; }

    /// <summary>
    /// Initializes the framework components asynchronously
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the initialization operation</returns>
    Task InitializeAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Shuts down the framework and cleans up resources
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the shutdown operation</returns>
    Task ShutdownAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Core framework service implementation
/// </summary>
public sealed class FrameworkService : IFrameworkService, IDisposable
{
    private readonly ILogger<FrameworkService> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly SemaphoreSlim _initializationSemaphore = new(1, 1);
    private volatile bool _isInitialized;
    private volatile bool _isDisposed;

    /// <summary>
    /// Initializes a new instance of the <see cref="FrameworkService"/> class
    /// </summary>
    /// <param name="logger">Logger instance</param>
    /// <param name="serviceProvider">Service provider</param>
    public FrameworkService(ILogger<FrameworkService> logger, IServiceProvider serviceProvider)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
    }

    /// <summary>
    /// Gets whether the framework is initialized
    /// </summary>
    public bool IsInitialized => _isInitialized;

    /// <summary>
    /// Initializes the framework components asynchronously
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the initialization operation</returns>
    public async Task InitializeAsync(CancellationToken cancellationToken = default)
    {
        if (_isInitialized)
            return;

        await _initializationSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (_isInitialized)
                return;

            _logger.LogInformation("Initializing ArtDesignFramework...");

            // Initialize core services through dependency injection
            var renderEngine = _serviceProvider.GetService<IRenderEngine>();
            if (renderEngine != null)
            {
                await renderEngine.InitializeAsync(cancellationToken);
            }

            _isInitialized = true;
            _logger.LogInformation("Framework initialization completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize framework");
            throw;
        }
        finally
        {
            _initializationSemaphore.Release();
        }
    }

    /// <summary>
    /// Shuts down the framework and cleans up resources
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the shutdown operation</returns>
    public async Task ShutdownAsync(CancellationToken cancellationToken = default)
    {
        if (_isDisposed)
            return;

        _logger.LogInformation("Shutting down ArtDesignFramework...");

        try
        {
            var renderEngine = _serviceProvider.GetService<IRenderEngine>();
            if (renderEngine != null)
            {
                await renderEngine.ShutdownAsync(cancellationToken);
            }

            _isInitialized = false;
            _logger.LogInformation("Framework shutdown completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during framework shutdown");
            throw;
        }
        finally
        {
            _isDisposed = true;
        }
    }

    /// <summary>
    /// Disposes the framework service and releases resources
    /// </summary>
    public void Dispose()
    {
        if (_isDisposed)
            return;

        try
        {
            ShutdownAsync().GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during framework disposal");
        }
        finally
        {
            _initializationSemaphore?.Dispose();
            _isDisposed = true;
        }
    }
}
