using System;

namespace ArtDesignFramework.Abstractions
{
    /// <summary>
    /// Attribute to mark classes as testable components for automated test generation
    /// Last Updated: 2025-01-11 15:30:00 UTC
    /// </summary>
    [AttributeUsage(AttributeTargets.Class)]
    public class TestableAttribute : Attribute
    {
        /// <summary>
        /// Gets or sets the test category for the marked class
        /// </summary>
        public string Category { get; set; } = "General";

        /// <summary>
        /// Gets or sets whether integration tests should be generated
        /// </summary>
        public bool IncludeIntegrationTests { get; set; } = true;

        /// <summary>
        /// Gets or sets whether performance tests should be generated
        /// </summary>
        public bool IncludePerformanceTests { get; set; }

        /// <summary>
        /// Initializes a new instance of the TestableAttribute class
        /// </summary>
        public TestableAttribute()
        {
        }

        /// <summary>
        /// Initializes a new instance of the TestableAttribute class with a specific category
        /// </summary>
        /// <param name="category">The test category for the marked class</param>
        public TestableAttribute(string category)
        {
            Category = category;
        }
    }

    /// <summary>
    /// Attribute to mark methods for specific test generation
    /// Last Updated: 2025-01-11 15:30:00 UTC
    /// </summary>
    [AttributeUsage(AttributeTargets.Method)]
    public class TestableMethodAttribute : Attribute
    {
        /// <summary>
        /// Gets or sets the test category for the marked method
        /// </summary>
        public string Category { get; set; } = "Method";

        /// <summary>
        /// Gets or sets whether to generate parameter validation tests
        /// </summary>
        public bool IncludeParameterValidation { get; set; } = true;

        /// <summary>
        /// Gets or sets whether to generate performance tests
        /// </summary>
        public bool IncludePerformanceTests { get; set; }

        /// <summary>
        /// Gets or sets whether to generate exception handling tests
        /// </summary>
        public bool IncludeExceptionTests { get; set; } = true;

        /// <summary>
        /// Gets or sets whether to generate concurrency tests
        /// </summary>
        public bool IncludeConcurrencyTests { get; set; }

        /// <summary>
        /// Gets or sets the expected execution time in milliseconds for performance tests
        /// </summary>
        public int ExpectedExecutionTimeMs { get; set; } = 1000;

        /// <summary>
        /// Gets or sets the number of concurrent tasks for concurrency tests
        /// </summary>
        public int ConcurrentTasks { get; set; } = 10;

        /// <summary>
        /// Initializes a new instance of the TestableMethodAttribute class
        /// </summary>
        public TestableMethodAttribute()
        {
        }

        /// <summary>
        /// Initializes a new instance of the TestableMethodAttribute class with a specific category
        /// </summary>
        /// <param name="category">The test category for the marked method</param>
        public TestableMethodAttribute(string category)
        {
            Category = category;
        }
    }

    /// <summary>
    /// Attribute to mark properties for get/set testing
    /// Last Updated: 2025-01-11 15:30:00 UTC
    /// </summary>
    [AttributeUsage(AttributeTargets.Property)]
    public class TestablePropertyAttribute : Attribute
    {
        /// <summary>
        /// Gets or sets the test category for the marked property
        /// </summary>
        public string Category { get; set; } = "Property";

        /// <summary>
        /// Gets or sets whether to generate getter tests
        /// </summary>
        public bool IncludeGetterTests { get; set; } = true;

        /// <summary>
        /// Gets or sets whether to generate setter tests
        /// </summary>
        public bool IncludeSetterTests { get; set; } = true;

        /// <summary>
        /// Gets or sets whether to generate null value tests
        /// </summary>
        public bool IncludeNullTests { get; set; } = true;

        /// <summary>
        /// Gets or sets whether to generate boundary value tests
        /// </summary>
        public bool IncludeBoundaryTests { get; set; }

        /// <summary>
        /// Gets or sets whether to generate type safety tests
        /// </summary>
        public bool IncludeTypeSafetyTests { get; set; } = true;

        /// <summary>
        /// Gets or sets test values to use for testing (JSON array format)
        /// </summary>
        public string? TestValues { get; set; }

        /// <summary>
        /// Gets or sets invalid values to test error handling (JSON array format)
        /// </summary>
        public string? InvalidValues { get; set; }

        /// <summary>
        /// Initializes a new instance of the TestablePropertyAttribute class
        /// </summary>
        public TestablePropertyAttribute()
        {
        }

        /// <summary>
        /// Initializes a new instance of the TestablePropertyAttribute class with a specific category
        /// </summary>
        /// <param name="category">The test category for the marked property</param>
        public TestablePropertyAttribute(string category)
        {
            Category = category;
        }
    }
}
