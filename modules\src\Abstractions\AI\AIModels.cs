// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using System;
using System.Collections.Generic;
using System.Drawing;

namespace ArtDesignFramework.Abstractions.AI
{
    /// <summary>
    /// AI performance metrics
    /// Last Updated: 2025-01-12 07:55:00 UTC
    /// </summary>
    public class AIMetrics
    {
        /// <summary>
        /// Gets or sets the processing speed in operations per second
        /// </summary>
        public double ProcessingSpeed { get; set; }

        /// <summary>
        /// Gets or sets the accuracy score as a percentage (0-100)
        /// </summary>
        public double AccuracyScore { get; set; }

        /// <summary>
        /// Gets or sets the learning rate for the AI model
        /// </summary>
        public double LearningRate { get; set; }

        /// <summary>
        /// Gets or sets the total number of predictions generated
        /// </summary>
        public int PredictionsGenerated { get; set; }

        /// <summary>
        /// Gets or sets the total number of optimizations applied
        /// </summary>
        public int OptimizationsApplied { get; set; }

        /// <summary>
        /// Gets or sets the system uptime duration
        /// </summary>
        public TimeSpan Uptime { get; set; }

        /// <summary>
        /// Gets or sets the memory usage as a percentage (0-100)
        /// </summary>
        public double MemoryUsagePercent { get; set; }

        /// <summary>
        /// Gets or sets the CPU usage as a percentage (0-100)
        /// </summary>
        public double CPUUsagePercent { get; set; }
    }

    /// <summary>
    /// Performance recommendation from AI analysis
    /// Last Updated: 2025-01-12 07:55:00 UTC
    /// </summary>
    public class PerformanceRecommendation
    {
        /// <summary>
        /// Gets or sets the type of recommendation
        /// </summary>
        public string RecommendationType { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the description of the recommendation
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the expected performance improvement percentage
        /// </summary>
        public double ExpectedImprovement { get; set; }

        /// <summary>
        /// Gets or sets the parameters for implementing the recommendation
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; } = new();

        /// <summary>
        /// Gets or sets the priority level of the recommendation
        /// </summary>
        public int Priority { get; set; }

        /// <summary>
        /// Gets or sets the estimated time to implement the recommendation
        /// </summary>
        public TimeSpan EstimatedImplementationTime { get; set; }
    }

    /// <summary>
    /// Workload analysis data for AI processing
    /// Last Updated: 2025-01-12 07:55:00 UTC
    /// </summary>
    public class WorkloadAnalysis
    {
        /// <summary>
        /// Gets or sets the number of active users
        /// </summary>
        public int ActiveUsers { get; set; }

        /// <summary>
        /// Gets or sets the CPU usage percentage
        /// </summary>
        public double CPUUsage { get; set; }

        /// <summary>
        /// Gets or sets the memory usage percentage
        /// </summary>
        public double MemoryUsage { get; set; }

        /// <summary>
        /// Gets or sets the number of concurrent operations
        /// </summary>
        public int ConcurrentOperations { get; set; }

        /// <summary>
        /// Gets or sets the list of active modules
        /// </summary>
        public List<string> ActiveModules { get; set; } = new();

        /// <summary>
        /// Gets or sets the performance metrics dictionary
        /// </summary>
        public Dictionary<string, double> PerformanceMetrics { get; set; } = new();
    }

    /// <summary>
    /// Result of AI optimization
    /// Last Updated: 2025-01-12 07:55:00 UTC
    /// </summary>
    public class OptimizationResult
    {
        /// <summary>
        /// Gets or sets whether the optimization was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Gets or sets the description of the optimization result
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the performance gain achieved
        /// </summary>
        public double PerformanceGain { get; set; }

        /// <summary>
        /// Gets or sets the list of optimizations that were applied
        /// </summary>
        public List<string> OptimizationsApplied { get; set; } = new();

        /// <summary>
        /// Gets or sets the time taken to perform the optimization
        /// </summary>
        public TimeSpan OptimizationTime { get; set; }

        /// <summary>
        /// Gets or sets the new settings after optimization
        /// </summary>
        public Dictionary<string, object> NewSettings { get; set; } = new();
    }

    /// <summary>
    /// Predicted issue identified by AI
    /// Last Updated: 2025-01-12 07:55:00 UTC
    /// </summary>
    public class PredictedIssue
    {
        /// <summary>
        /// Gets or sets the type of the predicted issue
        /// </summary>
        public string IssueType { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the description of the predicted issue
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the probability of the issue occurring (0-1)
        /// </summary>
        public double Probability { get; set; }

        /// <summary>
        /// Gets or sets the predicted time when the issue will occur
        /// </summary>
        public DateTime PredictedOccurrence { get; set; }

        /// <summary>
        /// Gets or sets the severity level of the issue
        /// </summary>
        public string Severity { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the list of preventive measures for the issue
        /// </summary>
        public List<string> PreventiveMeasures { get; set; } = new();
    }

    /// <summary>
    /// Recovery strategy generated by AI
    /// Last Updated: 2025-01-12 07:55:00 UTC
    /// </summary>
    public class RecoveryStrategy
    {
        /// <summary>
        /// Gets or sets the name of the recovery strategy
        /// </summary>
        public string StrategyName { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the description of the recovery strategy
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the steps to execute the recovery strategy
        /// </summary>
        public List<string> Steps { get; set; } = new();

        /// <summary>
        /// Gets or sets the probability of success for this strategy (0-1)
        /// </summary>
        public double SuccessProbability { get; set; }

        /// <summary>
        /// Gets or sets the estimated time to complete the recovery
        /// </summary>
        public TimeSpan EstimatedRecoveryTime { get; set; }

        /// <summary>
        /// Gets or sets whether user intervention is required
        /// </summary>
        public bool RequiresUserIntervention { get; set; }
    }

    /// <summary>
    /// User behavior data for AI learning
    /// Last Updated: 2025-01-12 07:55:00 UTC
    /// </summary>
    public class UserBehaviorData
    {
        /// <summary>
        /// Gets or sets the unique identifier of the user
        /// </summary>
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the action performed by the user
        /// </summary>
        public string Action { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the timestamp when the action occurred
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Gets or sets the context information for the action
        /// </summary>
        public Dictionary<string, object> Context { get; set; } = new();

        /// <summary>
        /// Gets or sets the result of the user action
        /// </summary>
        public string Result { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the duration of the user action
        /// </summary>
        public TimeSpan Duration { get; set; }
    }

    /// <summary>
    /// UI improvement suggestion from AI
    /// Last Updated: 2025-01-12 07:55:00 UTC
    /// </summary>
    public class UISuggestion
    {
        /// <summary>
        /// Gets or sets the type of UI suggestion
        /// </summary>
        public string SuggestionType { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the description of the UI suggestion
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the target UI element for the suggestion
        /// </summary>
        public string TargetElement { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the impact score of implementing this suggestion
        /// </summary>
        public double ImpactScore { get; set; }

        /// <summary>
        /// Gets or sets the implementation details for the suggestion
        /// </summary>
        public Dictionary<string, object> Implementation { get; set; } = new();
    }

    /// <summary>
    /// Code optimization suggestion from AI
    /// Last Updated: 2025-01-12 07:55:00 UTC
    /// </summary>
    public class CodeOptimization
    {
        /// <summary>
        /// Gets or sets the type of code optimization
        /// </summary>
        public string OptimizationType { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the description of the optimization
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the original code before optimization
        /// </summary>
        public string OriginalCode { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the optimized code
        /// </summary>
        public string OptimizedCode { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the expected performance gain from the optimization
        /// </summary>
        public double PerformanceGain { get; set; }

        /// <summary>
        /// Gets or sets the justification for the optimization
        /// </summary>
        public string Justification { get; set; } = string.Empty;
    }
}
