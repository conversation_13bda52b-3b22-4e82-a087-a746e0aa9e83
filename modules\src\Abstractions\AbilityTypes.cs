using System;
using System.Collections.Generic;

namespace ArtDesignFramework.Abstractions
{
    /// <summary>
    /// Categories for organizing abilities.
    /// Last Updated: 2025-01-11 15:30:00 UTC
    /// </summary>
    public enum AbilityCategory
    {
        /// <summary>
        /// Core framework abilities.
        /// </summary>
        Core,

        /// <summary>
        /// Rendering and graphics abilities.
        /// </summary>
        Rendering,

        /// <summary>
        /// Layer management abilities.
        /// </summary>
        LayerManagement,

        /// <summary>
        /// Command processing abilities.
        /// </summary>
        Command,

        /// <summary>
        /// Plugin system abilities.
        /// </summary>
        Plugin,

        /// <summary>
        /// Effects processing abilities.
        /// </summary>
        Effects,

        /// <summary>
        /// Performance monitoring abilities.
        /// </summary>
        Performance,

        /// <summary>
        /// User interface abilities.
        /// </summary>
        UserInterface,

        /// <summary>
        /// AI and machine learning abilities.
        /// </summary>
        AI,

        /// <summary>
        /// Data access abilities.
        /// </summary>
        DataAccess,

        /// <summary>
        /// Utility abilities.
        /// </summary>
        Utility
    }

    /// <summary>
    /// Status of an ability.
    /// Last Updated: 2025-01-11 15:30:00 UTC
    /// </summary>
    public enum AbilityStatus
    {
        /// <summary>
        /// Ability is not initialized.
        /// </summary>
        NotInitialized,

        /// <summary>
        /// Ability is initializing.
        /// </summary>
        Initializing,

        /// <summary>
        /// Ability is initialized but not active.
        /// </summary>
        Initialized,

        /// <summary>
        /// Ability is active and ready for use.
        /// </summary>
        Active,

        /// <summary>
        /// Ability is deactivating.
        /// </summary>
        Deactivating,

        /// <summary>
        /// Ability is inactive.
        /// </summary>
        Inactive,

        /// <summary>
        /// Ability has encountered an error.
        /// </summary>
        Error,

        /// <summary>
        /// Ability is disposed.
        /// </summary>
        Disposed
    }

    /// <summary>
    /// Represents a dependency between abilities.
    /// Last Updated: 2025-01-11 15:30:00 UTC
    /// </summary>
    public class AbilityDependency
    {
        /// <summary>
        /// Gets or sets the identifier of the required ability.
        /// </summary>
        public string AbilityId { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the minimum version required.
        /// </summary>
        public Version? MinVersion { get; set; }

        /// <summary>
        /// Gets or sets the maximum version supported.
        /// </summary>
        public Version? MaxVersion { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this dependency is optional.
        /// </summary>
        public bool IsOptional { get; set; }

        /// <summary>
        /// Gets or sets the reason for this dependency.
        /// </summary>
        public string Reason { get; set; } = string.Empty;
    }

    /// <summary>
    /// Performance characteristics of an ability.
    /// Last Updated: 2025-01-11 15:30:00 UTC
    /// </summary>
    public class AbilityPerformanceInfo
    {
        /// <summary>
        /// Gets or sets the expected memory usage in MB.
        /// </summary>
        public double ExpectedMemoryUsageMB { get; set; }

        /// <summary>
        /// Gets or sets the expected CPU usage percentage.
        /// </summary>
        public double ExpectedCpuUsagePercent { get; set; }

        /// <summary>
        /// Gets or sets the initialization time in milliseconds.
        /// </summary>
        public double InitializationTimeMs { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this ability is CPU intensive.
        /// </summary>
        public bool IsCpuIntensive { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this ability is memory intensive.
        /// </summary>
        public bool IsMemoryIntensive { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this ability requires GPU resources.
        /// </summary>
        public bool RequiresGpu { get; set; }
    }

    /// <summary>
    /// Context provided during ability initialization.
    /// Last Updated: 2025-01-11 15:30:00 UTC
    /// </summary>
    public interface IAbilityContext
    {
        /// <summary>
        /// Gets the registry that owns this ability.
        /// </summary>
        IAbilityRegistry Registry { get; }

        /// <summary>
        /// Gets the configuration settings for this ability.
        /// </summary>
        IReadOnlyDictionary<string, object> Configuration { get; }

        /// <summary>
        /// Gets the logger for this ability.
        /// </summary>
        IAbilityLogger Logger { get; }

        /// <summary>
        /// Gets the service provider for dependency injection.
        /// </summary>
        IServiceProvider ServiceProvider { get; }
    }

    /// <summary>
    /// Logger interface for abilities.
    /// Last Updated: 2025-01-11 15:30:00 UTC
    /// </summary>
    public interface IAbilityLogger
    {
        /// <summary>
        /// Logs an information message.
        /// </summary>
        /// <param name="message">The message to log.</param>
        void LogInformation(string message);

        /// <summary>
        /// Logs a warning message.
        /// </summary>
        /// <param name="message">The message to log.</param>
        void LogWarning(string message);

        /// <summary>
        /// Logs an error message.
        /// </summary>
        /// <param name="message">The message to log.</param>
        /// <param name="exception">The exception to log.</param>
        void LogError(string message, Exception? exception = null);

        /// <summary>
        /// Logs a debug message.
        /// </summary>
        /// <param name="message">The message to log.</param>
        void LogDebug(string message);
    }

    /// <summary>
    /// Result of ability validation.
    /// Last Updated: 2025-01-11 15:30:00 UTC
    /// </summary>
    public class AbilityValidationResult
    {
        /// <summary>
        /// Gets or sets a value indicating whether the validation was successful.
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Gets or sets the validation messages.
        /// </summary>
        public List<string> Messages { get; set; } = new();

        /// <summary>
        /// Gets or sets the missing dependencies.
        /// </summary>
        public List<string> MissingDependencies { get; set; } = new();

        /// <summary>
        /// Gets or sets the incompatible dependencies.
        /// </summary>
        public List<string> IncompatibleDependencies { get; set; } = new();
    }

    /// <summary>
    /// Event arguments for ability status changes.
    /// Last Updated: 2025-01-11 15:30:00 UTC
    /// </summary>
    public class AbilityStatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// Initializes a new instance of the AbilityStatusChangedEventArgs class.
        /// </summary>
        /// <param name="ability">The ability whose status changed.</param>
        /// <param name="oldStatus">The old status.</param>
        /// <param name="newStatus">The new status.</param>
        public AbilityStatusChangedEventArgs(IAbility ability, AbilityStatus oldStatus, AbilityStatus newStatus)
        {
            Ability = ability;
            OldStatus = oldStatus;
            NewStatus = newStatus;
        }

        /// <summary>
        /// Gets the ability whose status changed.
        /// </summary>
        public IAbility Ability { get; }

        /// <summary>
        /// Gets the old status.
        /// </summary>
        public AbilityStatus OldStatus { get; }

        /// <summary>
        /// Gets the new status.
        /// </summary>
        public AbilityStatus NewStatus { get; }
    }

    /// <summary>
    /// Event arguments for ability registration.
    /// Last Updated: 2025-01-11 15:30:00 UTC
    /// </summary>
    public class AbilityRegisteredEventArgs : EventArgs
    {
        /// <summary>
        /// Initializes a new instance of the AbilityRegisteredEventArgs class.
        /// </summary>
        /// <param name="ability">The registered ability.</param>
        public AbilityRegisteredEventArgs(IAbility ability)
        {
            Ability = ability;
        }

        /// <summary>
        /// Gets the registered ability.
        /// </summary>
        public IAbility Ability { get; }
    }
}
