<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <!-- TestFramework module specific settings -->
    <AssemblyTitle>ArtDesignFramework TestFramework Module</AssemblyTitle>
    <AssemblyDescription>Enterprise-grade comprehensive testing framework with automated validation and monitoring</AssemblyDescription>

    <!-- TestFramework specific overrides -->
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningsNotAsErrors>CS1591;SA1309;SA1101;SA1503;SA1629;SA1633;SA1649;SA1516;SA1402;SA1513;SA1515;SA1623;SA1413;SA1116;SA1108;SA1122;SA1028;IDE0028;IDE0052</WarningsNotAsErrors>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <PackageVersion>2.0.0</PackageVersion>
  </PropertyGroup>

  <ItemGroup>
    <!-- Module-specific packages not provided by Directory.Build.props -->
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.0" />
    <PackageReference Include="BenchmarkDotNet" Version="0.13.12" />
    <PackageReference Include="System.Diagnostics.PerformanceCounter" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Abstractions\ArtDesignFramework.Abstractions.csproj" />
    <ProjectReference Include="..\Core\ArtDesignFramework.Core.csproj" />
    <ProjectReference Include="..\DataAccess\ArtDesignFramework.DataAccess.csproj" />
    <!-- Temporarily commented out problematic dependencies for circular dependency testing -->
    <!-- <ProjectReference Include="..\Performance\ArtDesignFramework.Performance.csproj" /> -->
    <!-- <ProjectReference Include="..\UserInterface\ArtDesignFramework.UserInterface.csproj" /> -->
  </ItemGroup>

</Project>
