// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using System;
using System.Collections.Generic;
using System.Drawing;

namespace ArtDesignFramework.Abstractions.AI
{
    /// <summary>
    /// Canvas analysis data for AI processing and optimization suggestions.
    /// Last Updated: 2025-01-12 07:55:00 UTC
    /// </summary>
    public class CanvasAnalysisData
    {
        /// <summary>
        /// Gets or sets the canvas size.
        /// </summary>
        public Size CanvasSize { get; set; }

        /// <summary>
        /// Gets or sets the number of layers in the canvas.
        /// </summary>
        public int LayerCount { get; set; }

        /// <summary>
        /// Gets or sets the estimated memory usage in megabytes.
        /// </summary>
        public double EstimatedMemoryUsage { get; set; }

        /// <summary>
        /// Gets or sets whether the canvas has lighting data.
        /// </summary>
        public bool HasLightingData { get; set; }

        /// <summary>
        /// Gets or sets the average brightness of the canvas (0.0 to 1.0).
        /// </summary>
        public double AverageBrightness { get; set; }

        /// <summary>
        /// Gets or sets whether the canvas has complex brushwork.
        /// </summary>
        public bool HasComplexBrushwork { get; set; }

        /// <summary>
        /// Gets or sets whether face detection is present.
        /// </summary>
        public bool HasFaceDetection { get; set; }

        /// <summary>
        /// Gets or sets whether a horizon line is detected.
        /// </summary>
        public bool HasHorizonLine { get; set; }

        /// <summary>
        /// Gets or sets whether geometric shapes are present.
        /// </summary>
        public bool HasGeometricShapes { get; set; }

        /// <summary>
        /// Gets or sets the dominant color temperature.
        /// </summary>
        public int ColorTemperature { get; set; } = 5500;

        /// <summary>
        /// Gets or sets the contrast ratio (0.0 to 1.0).
        /// </summary>
        public double ContrastRatio { get; set; }
    }

    /// <summary>
    /// Canvas optimization suggestion generated by AI analysis.
    /// Last Updated: 2025-01-12 07:55:00 UTC
    /// </summary>
    public class CanvasOptimizationSuggestion
    {
        /// <summary>
        /// Gets or sets the type of optimization.
        /// </summary>
        public string OptimizationType { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the description of the optimization.
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the priority (1 = Critical, 2 = High, 3 = Medium, 4 = Low).
        /// </summary>
        public int Priority { get; set; }

        /// <summary>
        /// Gets or sets the expected performance improvement percentage.
        /// </summary>
        public double ExpectedImprovement { get; set; }

        /// <summary>
        /// Gets or sets the implementation parameters.
        /// </summary>
        public Dictionary<string, object> Implementation { get; set; } = new();

        /// <summary>
        /// Gets or sets the estimated implementation time.
        /// </summary>
        public TimeSpan EstimatedImplementationTime { get; set; }
    }

    /// <summary>
    /// Brush recommendation generated by AI analysis.
    /// Last Updated: 2025-01-12 07:55:00 UTC
    /// </summary>
    public class BrushRecommendation
    {
        /// <summary>
        /// Gets or sets the recommended brush type.
        /// </summary>
        public string BrushType { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the recommended brush size.
        /// </summary>
        public int RecommendedSize { get; set; }

        /// <summary>
        /// Gets or sets the recommended opacity (0.0 to 1.0).
        /// </summary>
        public double RecommendedOpacity { get; set; }

        /// <summary>
        /// Gets or sets the recommended flow (0.0 to 1.0).
        /// </summary>
        public double RecommendedFlow { get; set; }

        /// <summary>
        /// Gets or sets the recommended blend mode.
        /// </summary>
        public string BlendMode { get; set; } = "Normal";

        /// <summary>
        /// Gets or sets the justification for this recommendation.
        /// </summary>
        public string Justification { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the confidence score (0.0 to 1.0).
        /// </summary>
        public double ConfidenceScore { get; set; }

        /// <summary>
        /// Gets or sets the description of the recommendation.
        /// </summary>
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// Canvas lighting analysis data following LightingEngine patterns.
    /// Last Updated: 2025-01-12 07:55:00 UTC
    /// </summary>
    public class CanvasLightingAnalysis
    {
        /// <summary>
        /// Gets or sets the dominant light direction.
        /// </summary>
        public LightDirection DominantLightDirection { get; set; } = new();

        /// <summary>
        /// Gets or sets the average brightness (0.0 to 1.0).
        /// </summary>
        public double AverageBrightness { get; set; }

        /// <summary>
        /// Gets or sets the contrast ratio (0.0 to 1.0).
        /// </summary>
        public double Contrast { get; set; }

        /// <summary>
        /// Gets or sets the color temperature in Kelvin.
        /// </summary>
        public int ColorTemperature { get; set; }

        /// <summary>
        /// Gets or sets the suggested light sources.
        /// </summary>
        public List<SuggestedLightSource> SuggestedLights { get; set; } = new();
    }

    /// <summary>
    /// Light direction vector following LightingEngine patterns.
    /// Last Updated: 2025-01-12 07:55:00 UTC
    /// </summary>
    public class LightDirection
    {
        /// <summary>
        /// Gets or sets the X component.
        /// </summary>
        public double X { get; set; }

        /// <summary>
        /// Gets or sets the Y component.
        /// </summary>
        public double Y { get; set; }

        /// <summary>
        /// Gets or sets the Z component.
        /// </summary>
        public double Z { get; set; } = 1.0;
    }

    /// <summary>
    /// Suggested light source following LightingEngine patterns.
    /// Last Updated: 2025-01-12 07:55:00 UTC
    /// </summary>
    public class SuggestedLightSource
    {
        /// <summary>
        /// Gets or sets the light source ID.
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the light type.
        /// </summary>
        public string Type { get; set; } = "Directional";

        /// <summary>
        /// Gets or sets the light position.
        /// </summary>
        public LightDirection Position { get; set; } = new();

        /// <summary>
        /// Gets or sets the light direction.
        /// </summary>
        public LightDirection Direction { get; set; } = new();

        /// <summary>
        /// Gets or sets the light intensity (0.0 to 2.0).
        /// </summary>
        public double Intensity { get; set; } = 1.0;

        /// <summary>
        /// Gets or sets the light color.
        /// </summary>
        public LightColor Color { get; set; } = new();

        /// <summary>
        /// Gets or sets the depth position (-1.0 to 1.0).
        /// </summary>
        public double DepthPosition { get; set; } = 0.0;

        /// <summary>
        /// Gets or sets the strength multiplier (1.0 to 2.0).
        /// </summary>
        public double StrengthMultiplier { get; set; } = 1.0;
    }

    /// <summary>
    /// Light color representation.
    /// Last Updated: 2025-01-12 07:55:00 UTC
    /// </summary>
    public class LightColor
    {
        /// <summary>
        /// Gets or sets the red component (0-255).
        /// </summary>
        public int R { get; set; } = 255;

        /// <summary>
        /// Gets or sets the green component (0-255).
        /// </summary>
        public int G { get; set; } = 255;

        /// <summary>
        /// Gets or sets the blue component (0-255).
        /// </summary>
        public int B { get; set; } = 255;
    }
}
