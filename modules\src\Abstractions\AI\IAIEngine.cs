// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ArtDesignFramework.Abstractions.AI
{
    /// <summary>
    /// Interface for the AI Engine - Revolutionary neural-powered intelligence system
    /// Last Updated: 2025-01-12 07:55:00 UTC
    /// </summary>
    public interface IAIEngine
    {
        /// <summary>
        /// Gets the current AI performance metrics
        /// </summary>
        AIMetrics CurrentMetrics { get; }

        /// <summary>
        /// Gets whether the AI engine is ready for operations
        /// </summary>
        bool IsReady { get; }

        /// <summary>
        /// Initializes the AI engine with neural networks
        /// </summary>
        Task<bool> InitializeAsync();

        /// <summary>
        /// Predicts optimal performance settings based on current workload
        /// </summary>
        Task<PerformanceRecommendation> PredictOptimalPerformanceAsync(WorkloadAnalysis workload);

        /// <summary>
        /// Auto-optimizes the framework based on usage patterns
        /// </summary>
        Task<OptimizationResult> AutoOptimizeAsync();

        /// <summary>
        /// Detects and predicts potential issues before they occur
        /// </summary>
        Task<List<PredictedIssue>> PredictIssuesAsync();

        /// <summary>
        /// Provides intelligent error recovery suggestions
        /// </summary>
        Task<List<RecoveryStrategy>> GenerateRecoveryStrategiesAsync(Exception exception);

        /// <summary>
        /// Learns from user behavior and adapts the framework
        /// </summary>
        Task LearnFromUserBehaviorAsync(UserBehaviorData behavior);

        /// <summary>
        /// Generates intelligent suggestions for UI/UX improvements
        /// </summary>
        Task<List<UISuggestion>> GenerateUISuggestionsAsync();

        /// <summary>
        /// Analyzes code patterns and suggests optimizations
        /// </summary>
        Task<List<CodeOptimization>> AnalyzeCodePatternsAsync(string code);

        /// <summary>
        /// Generates canvas optimization suggestions
        /// </summary>
        Task<List<CanvasOptimizationSuggestion>> GenerateCanvasOptimizationSuggestionsAsync(CanvasAnalysisData canvasData);

        /// <summary>
        /// Generates brush recommendations
        /// </summary>
        Task<List<BrushRecommendation>> GenerateBrushRecommendationsAsync(CanvasAnalysisData canvasData);

        /// <summary>
        /// Shuts down the AI engine gracefully
        /// </summary>
        Task ShutdownAsync();
    }
}
