{"testSettings": {"basic": {"fontFamily": "<PERSON><PERSON>", "fontSize": 48, "textColor": "#FFFFFF", "dateColor": "#CCCCCC", "backgroundColor": "#000000", "transparentBackground": false, "alwaysOnTop": true, "showDate": true, "width": 800, "height": 600, "left": 100, "top": 100, "opacity": 1.0}, "transparent": {"fontFamily": "<PERSON><PERSON>", "fontSize": 48, "textColor": "#FFFFFF", "dateColor": "#CCCCCC", "backgroundColor": "#000000", "transparentBackground": true, "alwaysOnTop": true, "showDate": true, "width": 800, "height": 600, "left": 100, "top": 100, "opacity": 1.0, "transparencyLevel": 0.8, "enableClickThrough": true, "usePerPixelTransparency": true, "enableSmoothTransitions": true}, "withEffects": {"fontFamily": "<PERSON><PERSON>", "fontSize": 48, "textColor": "#FFFFFF", "dateColor": "#CCCCCC", "backgroundColor": "#000000", "transparentBackground": false, "alwaysOnTop": true, "showDate": true, "width": 800, "height": 600, "left": 100, "top": 100, "opacity": 1.0, "enableGlow": true, "glowRadius": 10, "glowColor": "#FFFF00", "enableShadow": true, "shadowOffsetX": 3, "shadowOffsetY": 3, "shadowBlur": 5, "shadowColor": "#000000", "enableStroke": true, "strokeWidth": 2, "strokeColor": "#FFFFFF", "enable3D": true, "textDepth": 15, "bevelSize": 2, "text3DColor": "#C0C0C0"}}, "performanceTargets": {"renderingTimeMs": 16.67, "memoryReductionPercent": 70, "poolEfficiencyPercent": 95, "targetFps": 60, "maxFrameTimeMs": 50}, "testScenarios": [{"name": "BasicRendering", "description": "Test basic clock rendering without effects", "settings": "basic", "expectedScore": 90}, {"name": "TransparencyMode", "description": "Test transparency and click-through functionality", "settings": "transparent", "expectedScore": 85}, {"name": "FullEffects", "description": "Test rendering with all effects enabled", "settings": "withEffects", "expectedScore": 80}], "fontTestData": ["<PERSON><PERSON>", "Times New Roman", "Courier New", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Georgia", "Comic Sans MS", "Impact", "Trebuchet MS", "Luc<PERSON> Con<PERSON>"], "colorTestData": ["#FFFFFF", "#000000", "#FF0000", "#00FF00", "#0000FF", "#FFFF00", "#FF00FF", "#00FFFF", "#808080", "#C0C0C0"]}