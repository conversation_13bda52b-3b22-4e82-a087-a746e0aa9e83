// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using System.Diagnostics;
using ArtDesignFramework.ClockDesktopApp.Services;
using ArtDesignFramework.ClockDesktopApp.Tests.Helpers;
using ArtDesignFramework.ClockDesktopApp.ViewModels;
using ArtDesignFramework.Core.AI;
using ArtDesignFramework.Core.Attributes;
using ArtDesignFramework.UserInterface.Services.Rendering;
using ArtDesignFramework.UserInterface.Tools;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using SkiaSharp;
using Xunit;

namespace ArtDesignFramework.ClockDesktopApp.Tests.Performance;

/// <summary>
/// Performance tests for ClockDesktopApp rendering operations
/// Last Updated: 2025-01-27 22:45:00 UTC
/// </summary>
[Testable("ClockRenderingPerformance", IncludePerformanceTests = true, IncludeIntegrationTests = true)]
public class ClockRenderingPerformanceTests : IDisposable
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ISKPaintPool _paintPool;
    private readonly ClockWorkshopViewModel _viewModel;

    public ClockRenderingPerformanceTests()
    {
        _serviceProvider = TestHelper.CreateTestServiceProvider();
        _paintPool = _serviceProvider.GetRequiredService<ISKPaintPool>();

        // Create view model with all dependencies
        var logger = _serviceProvider.GetRequiredService<ILogger<ClockWorkshopViewModel>>();
        var widgetService = _serviceProvider.GetRequiredService<ClockWidgetService>();
        var settingsService = _serviceProvider.GetRequiredService<SettingsService>();
        var enhancedFontService = _serviceProvider.GetRequiredService<EnhancedFontLoadingService>();

        // Create mock dependencies for view model
        var dateComponentManager = new Mock<DateComponentManager>().Object;
        var dragDropService = new Mock<DragDropPreviewService>().Object;
        var layerManager = new Mock<ClockLayerManager>().Object;
        var enhanced3DRenderer = new Mock<IEnhanced3DTextRenderer>().Object;
        var selectionToolsEngine = new Mock<SelectionToolsEngine>().Object;
        var aiEngine = new Mock<IAIEngine>().Object;

        _viewModel = new ClockWorkshopViewModel(
            logger, widgetService, settingsService, dateComponentManager,
            dragDropService, layerManager, enhanced3DRenderer, _paintPool,
            selectionToolsEngine, aiEngine, enhancedFontService,
            TestHelper.CreateMockPerformanceMonitor(),
            TestHelper.CreateMockRenderingProfiler(),
            TestHelper.CreateMockMemoryProfiler());
    }

    /// <summary>
    /// Tests 60 FPS rendering capability (16.67ms per frame target)
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [Fact]
    [TestableMethod("SixtyFpsRenderingCapability", IncludePerformanceTests = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 17)]
    public void ClockRendering_ShouldMeet60FpsTarget()
    {
        // Arrange
        var (canvas, bitmap) = TestHelper.CreateTestCanvas();
        var frameCount = 60; // Test 60 frames
        var frameTimes = new List<double>();

        try
        {
            // Act - Render 60 frames and measure each
            for (int i = 0; i < frameCount; i++)
            {
                var frameTime = TestHelper.MeasureExecutionTime(() =>
                {
                    _viewModel.RenderClock(canvas, 800, 600);
                });

                frameTimes.Add(frameTime.TotalMilliseconds);
            }

            // Assert
            var averageFrameTime = frameTimes.Average();
            var maxFrameTime = frameTimes.Max();
            var targetFrameTime = 16.67; // 60 FPS

            averageFrameTime.Should().BeLessOrEqualTo(targetFrameTime,
                $"Average frame time should be <= {targetFrameTime}ms for 60 FPS");
            maxFrameTime.Should().BeLessOrEqualTo(targetFrameTime * 2,
                $"Max frame time should be <= {targetFrameTime * 2}ms to prevent frame drops");

            // Validate 60 FPS capability
            TestHelper.Validate60FpsCapability(TimeSpan.FromMilliseconds(averageFrameTime)).Should().BeTrue();
        }
        finally
        {
            canvas.Dispose();
            bitmap.Dispose();
        }
    }

    /// <summary>
    /// Tests SKPaint pooling memory reduction (70% target)
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [Fact]
    [TestableMethod("SKPaintPoolingMemoryReduction", IncludePerformanceTests = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 2000)]
    public void SKPaintPooling_ShouldAchieve70PercentMemoryReduction()
    {
        // Arrange
        const int operationCount = 1000;
        var (canvas, bitmap) = TestHelper.CreateTestCanvas();

        try
        {
            // Measure baseline memory usage without pooling
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            var baselineMemoryBefore = GC.GetTotalMemory(false);

            // Simulate rendering without pooling (create new paints each time)
            for (int i = 0; i < operationCount; i++)
            {
                using var paint = new SKPaint();
                paint.Color = SKColors.White;
                paint.TextSize = 48;
                paint.IsAntialias = true;
                // Simulate rendering operations
                canvas.DrawText("TEST", 100, 100, paint);
            }

            var baselineMemoryAfter = GC.GetTotalMemory(true);
            var baselineMemoryUsed = baselineMemoryAfter - baselineMemoryBefore;

            // Reset for pooled test
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            var pooledMemoryBefore = GC.GetTotalMemory(false);

            // Test with pooling
            for (int i = 0; i < operationCount; i++)
            {
                var paint = _paintPool.Get();
                paint.Color = SKColors.White;
                paint.TextSize = 48;
                paint.IsAntialias = true;
                // Simulate rendering operations
                canvas.DrawText("TEST", 100, 100, paint);
                _paintPool.Return(paint);
            }

            var pooledMemoryAfter = GC.GetTotalMemory(true);
            var pooledMemoryUsed = pooledMemoryAfter - pooledMemoryBefore;

            // Assert - Validate 70% memory reduction
            var memoryReduction = TestHelper.ValidateMemoryReduction(baselineMemoryUsed, pooledMemoryUsed, 0.70);
            memoryReduction.Should().BeTrue($"Should achieve 70% memory reduction. Baseline: {baselineMemoryUsed:N0} bytes, Pooled: {pooledMemoryUsed:N0} bytes");

            // Validate pool statistics
            var stats = _paintPool.GetStatistics();
            stats.PoolEfficiency.Should().BeGreaterOrEqualTo(0.95, "Pool efficiency should be >= 95%");
        }
        finally
        {
            canvas.Dispose();
            bitmap.Dispose();
        }
    }

    /// <summary>
    /// Tests sustained rendering performance over time
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [Fact]
    [TestableMethod("SustainedRenderingPerformance", IncludePerformanceTests = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 5000)]
    public void SustainedRendering_ShouldMaintainPerformance()
    {
        // Arrange
        var (canvas, bitmap) = TestHelper.CreateTestCanvas();
        const int testDurationSeconds = 3;
        const int targetFps = 60;
        var frameTimes = new List<double>();
        var stopwatch = Stopwatch.StartNew();

        try
        {
            // Act - Render for specified duration
            while (stopwatch.Elapsed.TotalSeconds < testDurationSeconds)
            {
                var frameTime = TestHelper.MeasureExecutionTime(() =>
                {
                    _viewModel.RenderClock(canvas, 800, 600);
                });

                frameTimes.Add(frameTime.TotalMilliseconds);

                // Simulate frame pacing
                Thread.Sleep(1);
            }

            stopwatch.Stop();

            // Assert
            var actualFrameCount = frameTimes.Count;
            var averageFrameTime = frameTimes.Average();
            var actualFps = actualFrameCount / stopwatch.Elapsed.TotalSeconds;

            // Validate sustained performance
            averageFrameTime.Should().BeLessOrEqualTo(16.67, "Average frame time should maintain 60 FPS capability");
            actualFps.Should().BeGreaterOrEqualTo(30, "Should maintain at least 30 FPS under sustained load");

            // Check for performance degradation over time
            var firstHalfAverage = frameTimes.Take(frameTimes.Count / 2).Average();
            var secondHalfAverage = frameTimes.Skip(frameTimes.Count / 2).Average();
            var performanceDegradation = (secondHalfAverage - firstHalfAverage) / firstHalfAverage;

            performanceDegradation.Should().BeLessOrEqualTo(0.1, "Performance degradation should be <= 10% over time");
        }
        finally
        {
            canvas.Dispose();
            bitmap.Dispose();
        }
    }

    /// <summary>
    /// Tests rendering performance with all effects enabled
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [Fact]
    [TestableMethod("FullEffectsRenderingPerformance", IncludePerformanceTests = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 50)]
    public void FullEffectsRendering_ShouldMeetPerformanceTargets()
    {
        // Arrange
        var (canvas, bitmap) = TestHelper.CreateTestCanvas();

        // Enable all effects for maximum load
        _viewModel.EnableGlow = true;
        _viewModel.EnableShadow = true;
        _viewModel.EnableStroke = true;
        _viewModel.Enable3D = true;
        _viewModel.ShowDate = true;

        try
        {
            // Act
            var executionTime = TestHelper.MeasureExecutionTime(() =>
            {
                _viewModel.RenderClock(canvas, 800, 600);
            });

            // Assert
            executionTime.TotalMilliseconds.Should().BeLessOrEqualTo(50,
                "Full effects rendering should complete within 50ms");

            TestHelper.ValidatePerformanceTargets(executionTime, 50).Should().BeTrue();
        }
        finally
        {
            canvas.Dispose();
            bitmap.Dispose();
        }
    }

    /// <summary>
    /// Tests memory usage during extended rendering sessions
    /// Last Updated: 2025-01-27 22:45:00 UTC
    /// </summary>
    [Fact]
    [TestableMethod("ExtendedRenderingMemoryUsage", IncludePerformanceTests = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 3000)]
    public void ExtendedRendering_ShouldNotLeakMemory()
    {
        // Arrange
        var (canvas, bitmap) = TestHelper.CreateTestCanvas();
        const int renderCount = 1000;

        try
        {
            // Baseline memory measurement
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            var initialMemory = GC.GetTotalMemory(false);

            // Act - Extended rendering session
            for (int i = 0; i < renderCount; i++)
            {
                _viewModel.RenderClock(canvas, 800, 600);

                // Periodic cleanup to simulate real usage
                if (i % 100 == 0)
                {
                    GC.Collect(0, GCCollectionMode.Optimized);
                }
            }

            // Final memory measurement
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            var finalMemory = GC.GetTotalMemory(false);

            // Assert
            var memoryIncrease = finalMemory - initialMemory;
            var memoryIncreasePerRender = memoryIncrease / (double)renderCount;

            memoryIncreasePerRender.Should().BeLessOrEqualTo(1024, // 1KB per render max
                $"Memory increase per render should be <= 1KB, was {memoryIncreasePerRender:F2} bytes");

            memoryIncrease.Should().BeLessOrEqualTo(1024 * 1024, // 1MB total max
                $"Total memory increase should be <= 1MB, was {memoryIncrease:N0} bytes");
        }
        finally
        {
            canvas.Dispose();
            bitmap.Dispose();
        }
    }

    public void Dispose()
    {
        _viewModel?.Dispose();
        (_serviceProvider as IDisposable)?.Dispose();
    }
}
