<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors>CS1591</WarningsNotAsErrors>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="Moq" Version="4.20.69" />
    <PackageReference Include="xunit" Version="2.4.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.4.5" />
    <PackageReference Include="FluentAssertions" Version="6.12.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
    <PackageReference Include="SkiaSharp" Version="2.88.8" />
    <PackageReference Include="SkiaSharp.Views.WPF" Version="2.88.8" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\modules\src\ClockDesktopApp\ArtDesignFramework.ClockDesktopApp.csproj" />
    <ProjectReference Include="..\..\modules\src\Core\ArtDesignFramework.Core.csproj" />
    <ProjectReference Include="..\..\modules\src\Performance\ArtDesignFramework.Performance.csproj" />
    <ProjectReference Include="..\..\modules\src\UserInterface\ArtDesignFramework.UserInterface.csproj" />
    <ProjectReference Include="..\..\modules\src\TestFramework\ArtDesignFramework.TestFramework.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Unit\Services\" />
    <Folder Include="Unit\ViewModels\" />
    <Folder Include="Unit\Views\" />
    <Folder Include="Integration\" />
    <Folder Include="Performance\" />
    <Folder Include="TestData\" />
    <Folder Include="Helpers\" />
  </ItemGroup>

</Project>
