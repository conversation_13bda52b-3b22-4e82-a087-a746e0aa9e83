// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using ArtDesignFramework.Abstractions;

namespace ArtDesignFramework.Core;

/// <summary>
/// Core effects engine for applying visual effects
/// </summary>
[Testable]
public sealed class EffectsEngine : IDisposable
{
    private readonly Dictionary<string, object> _effects = new();
    private bool _disposed;

    /// <summary>
    /// Gets all registered effects
    /// </summary>
    public IReadOnlyDictionary<string, object> Effects => _effects.AsReadOnly();

    /// <summary>
    /// Registers an effect
    /// </summary>
    /// <param name="effectId">Effect identifier</param>
    /// <param name="effect">Effect instance</param>
    public void RegisterEffect(string effectId, object effect)
    {
        ThrowIfDisposed();
        _effects[effectId] = effect;
        // TODO: Add proper logging via dependency injection
        Console.WriteLine($"Effect registered: {effectId}. Total effects: {_effects.Count}");
    }

    /// <summary>
    /// Unregisters an effect
    /// </summary>
    /// <param name="effectId">Effect identifier</param>
    /// <returns>True if the effect was unregistered</returns>
    public bool UnregisterEffect(string effectId)
    {
        ThrowIfDisposed();
        var removed = _effects.Remove(effectId);
        if (removed)
        {
            // TODO: Add proper logging via dependency injection
            Console.WriteLine($"Effect unregistered: {effectId}. Total effects: {_effects.Count}");
        }
        return removed;
    }

    /// <summary>
    /// Gets an effect by identifier
    /// </summary>
    /// <param name="effectId">Effect identifier</param>
    /// <returns>Effect instance or null if not found</returns>
    public object? GetEffect(string effectId)
    {
        ThrowIfDisposed();
        return _effects.TryGetValue(effectId, out var effect) ? effect : null;
    }

    /// <summary>
    /// Applies an effect to data
    /// </summary>
    /// <param name="effectId">Effect identifier</param>
    /// <param name="data">Data to process</param>
    /// <returns>Processed data</returns>
    public object? ApplyEffect(string effectId, object data)
    {
        ThrowIfDisposed();
        var effect = GetEffect(effectId);
        if (effect == null)
        {
            // TODO: Add proper logging via dependency injection
            Console.WriteLine($"Warning: Effect not found: {effectId}");
            return data;
        }

        // TODO: Add proper logging via dependency injection
        Console.WriteLine($"Applying effect: {effectId}");
        // Placeholder for actual effect application
        return data;
    }

    /// <summary>
    /// Clears all effects
    /// </summary>
    public void ClearEffects()
    {
        ThrowIfDisposed();
        _effects.Clear();
        // TODO: Add proper logging via dependency injection
        Console.WriteLine("All effects cleared");
    }

    /// <summary>
    /// Disposes the effects engine
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            ClearEffects();
            _disposed = true;
        }
    }

    private void ThrowIfDisposed()
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(EffectsEngine));
    }
}
